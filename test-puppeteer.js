// Simple test script to check if <PERSON><PERSON><PERSON><PERSON> can extract cookies from AIEase
const puppeteer = require('puppeteer');

async function testPuppeteer() {
  try {
    console.log('Testing Puppeteer cookie extraction...');
    
    const browser = await puppeteer.launch({ 
      headless: true,
      args: [
        '--no-sandbox', 
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });
    
    const page = await browser.newPage();
    
    // Set realistic browser headers
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
    
    console.log('Navigating to AIEase website...');
    await page.goto('https://www.aiease.ai/app/generate-images', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });

    console.log('Waiting for page to load...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Check Local Storage for userStore
    console.log('Checking Local Storage for userStore...');
    const userStoreData = await page.evaluate(() => {
      try {
        const userStore = localStorage.getItem('userStore');
        if (userStore) {
          return JSON.parse(userStore);
        }
        return null;
      } catch (error) {
        return { error: error.message };
      }
    });

    if (userStoreData && userStoreData.user?.token) {
      console.log(`\n✅ SUCCESS: Found userStore in Local Storage!`);
      console.log(`Token found: ${userStoreData.user.token.substring(0, 50)}...`);
      console.log(`User ID: ${userStoreData.user.id}`);
      console.log(`Username: ${userStoreData.user.username}`);
      console.log(`Email: ${userStoreData.user.email}`);
    } else if (userStoreData && userStoreData.error) {
      console.log(`\n❌ Error accessing localStorage: ${userStoreData.error}`);
    } else {
      console.log(`\n❌ No userStore found in Local Storage`);

      // Fallback: check cookies
      console.log('Fallback: checking cookies...');
      const cookies = await page.cookies('https://www.aiease.ai');
      console.log('Available cookies:', cookies.map(c => c.name));

      const userStoreCookie = cookies.find(cookie =>
        cookie.name === 'userStore'
      );

      if (userStoreCookie) {
        console.log(`\n✅ FALLBACK: Found userStore cookie!`);
        try {
          const cookieData = JSON.parse(decodeURIComponent(userStoreCookie.value));
          const token = cookieData.user?.token;

          if (token) {
            console.log(`Token found in cookie: ${token.substring(0, 50)}...`);
          }
        } catch (parseError) {
          console.log('❌ Failed to parse userStore cookie:', parseError.message);
        }
      }
    }
    
    await browser.close();
    
  } catch (error) {
    console.error('❌ Puppeteer test failed:', error.message);
  }
}

testPuppeteer();
