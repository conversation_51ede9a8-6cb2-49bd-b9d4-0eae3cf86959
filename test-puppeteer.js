// Simple test script to check if <PERSON><PERSON><PERSON><PERSON> can extract cookies from AIEase
const puppeteer = require('puppeteer');

async function testPuppeteer() {
  try {
    console.log('Testing Puppeteer cookie extraction...');
    
    const browser = await puppeteer.launch({ 
      headless: true,
      args: [
        '--no-sandbox', 
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });
    
    const page = await browser.newPage();
    
    // Set realistic browser headers
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
    
    console.log('Navigating to AIEase website...');
    await page.goto('https://www.aiease.ai/app/generate-images', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });

    console.log('Waiting for cookies to be set...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Get all cookies
    const cookies = await page.cookies('https://www.aiease.ai');
    console.log('All cookies found:');
    cookies.forEach(cookie => {
      console.log(`- ${cookie.name}: ${cookie.value.substring(0, 50)}...`);
    });
    
    // Look for userStore cookie which contains the actual token
    const userStoreCookie = cookies.find(cookie =>
      cookie.name === 'userStore'
    );

    if (userStoreCookie) {
      console.log(`\n✅ SUCCESS: Found userStore cookie!`);
      try {
        const userStoreData = JSON.parse(decodeURIComponent(userStoreCookie.value));
        const token = userStoreData.user?.token;

        if (token) {
          console.log(`Token found: ${token.substring(0, 50)}...`);
          console.log(`User ID: ${userStoreData.user?.id}`);
          console.log(`Username: ${userStoreData.user?.username}`);
        } else {
          console.log('❌ No token found in userStore cookie');
        }
      } catch (parseError) {
        console.log('❌ Failed to parse userStore cookie:', parseError.message);
      }
    } else {
      console.log(`\n❌ No userStore cookie found`);
      console.log('Available cookies:', cookies.map(c => c.name));

      // Fallback: look for _hjSessionUser_xxxx cookie
      const sessionCookie = cookies.find(cookie =>
        cookie.name.startsWith('_hjSessionUser_')
      );

      if (sessionCookie) {
        console.log(`\n✅ FALLBACK: Found _hjSessionUser cookie!`);
        console.log(`Cookie name: ${sessionCookie.name}`);
        console.log(`Cookie value: ${sessionCookie.value.substring(0, 50)}...`);
      }
    }
    
    await browser.close();
    
  } catch (error) {
    console.error('❌ Puppeteer test failed:', error.message);
  }
}

testPuppeteer();
