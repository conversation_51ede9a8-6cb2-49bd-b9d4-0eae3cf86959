const fetch = require('node-fetch');

const API_BASE = 'https://fluxapi-nodejs.freecode911.deno.net';

// Test data for the endpoints
const testCases = [
  {
    name: "Test /v1/chat/completions with valid data",
    endpoint: "/v1/chat/completions",
    method: "POST",
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-proxy-secret': 'dcbb12e0-1ad1-11f0-9372-ed5f9f2fbfb2'
    },
    body: {
      model: "Llama-3.3-70B-Instruct-Turbo",
      messages: [
        { role: "user", content: "Hello, how are you?" }
      ],
      max_tokens: 100,
      temperature: 0.7,
      stream: false
    }
  },
  {
    name: "Test /v1/chat/completions/custom with valid data",
    endpoint: "/v1/chat/completions/custom",
    method: "POST",
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-proxy-secret': 'dcbb12e0-1ad1-11f0-9372-ed5f9f2fbfb2'
    },
    body: {
      model: "grok-3-mini",
      messages: [
        { role: "user", content: "Hello, how are you?" }
      ],
      max_tokens: 100,
      temperature: 0.7,
      stream: false
    }
  },
  {
    name: "Test /v1/chat/completions without model (should fail)",
    endpoint: "/v1/chat/completions",
    method: "POST",
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-proxy-secret': 'dcbb12e0-1ad1-11f0-9372-ed5f9f2fbfb2'
    },
    body: {
      messages: [
        { role: "user", content: "Hello, how are you?" }
      ]
    }
  },
  {
    name: "Test /v1/chat/completions without messages (should fail)",
    endpoint: "/v1/chat/completions",
    method: "POST",
    headers: {
      'Content-Type': 'application/json',
      'x-rapidapi-proxy-secret': 'dcbb12e0-1ad1-11f0-9372-ed5f9f2fbfb2'
    },
    body: {
      model: "Llama-3.3-70B-Instruct-Turbo"
    }
  }
];

async function runTest(testCase) {
  console.log(`\n🧪 ${testCase.name}`);
  console.log(`📤 ${testCase.method} ${testCase.endpoint}`);
  
  try {
    const response = await fetch(`${API_BASE}${testCase.endpoint}`, {
      method: testCase.method,
      headers: testCase.headers,
      body: JSON.stringify(testCase.body)
    });

    const responseText = await response.text();
    console.log(`📊 Status: ${response.status}`);
    console.log(`📋 Response: ${responseText.substring(0, 500)}${responseText.length > 500 ? '...' : ''}`);
    
    if (response.status >= 400) {
      console.log(`❌ Test failed as expected or with error`);
    } else {
      console.log(`✅ Test passed`);
    }
  } catch (error) {
    console.log(`💥 Error: ${error.message}`);
  }
}

async function runAllTests() {
  console.log('🚀 Starting endpoint tests...');
  
  for (const testCase of testCases) {
    await runTest(testCase);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
  }
  
  console.log('\n🏁 All tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, runTest };
