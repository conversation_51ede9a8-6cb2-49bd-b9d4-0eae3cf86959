const express = require('express');
const cors = require('cors');
const { default: Together } = require('together-ai');
const { v4: uuidv4 } = require('uuid');
const UAParser = require('ua-parser-js');
const crypto = require('crypto');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// No storage needed - using stateless POST requests with message history

// Express middleware
app.use(cors());

// Custom JSON parser that handles missing Content-Type from RapidAPI
app.use(express.raw({ type: '*/*', limit: '10mb' }));
app.use((req, res, next) => {
  // If we have raw data and no parsed body, try to parse as JSON
  if (req.body && Buffer.isBuffer(req.body) && req.body.length > 0) {
    try {
      const bodyString = req.body.toString('utf8');
      // Check if it looks like JSO<PERSON>
      if (bodyString.trim().startsWith('{') || bodyString.trim().startsWith('[')) {
        req.body = JSON.parse(bodyString);
        console.log('Parsed JSON from raw body:', req.body);
      }
    } catch (error) {
      console.log('Failed to parse raw body as JSON:', error.message);
      // If parsing fails, continue with raw body
    }
  }
  next();
});

// Fallback JSON parser for properly formatted requests
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Error handling middleware for JSON parsing
app.use((error, req, res, next) => {
  if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
    console.error('JSON parsing error:', error.message);
    return res.status(400).json({
      error: {
        message: "Invalid JSON in request body",
        type: "invalid_request_error",
        code: "json_parse_error",
        details: error.message
      }
    });
  }
  next();
});

// RapidAPI security configuration
const RAPIDAPI_PROXY_SECRET = process.env.RAPIDAPI_PROXY_SECRET || "dcbb12e0-1ad1-11f0-9372-ed5f9f2fbfb2";

// Middleware to validate RapidAPI requests
const validateRapidAPIRequest = (req, res, next) => {
  // Skip validation in development mode if needed
  if (process.env.DEV_MODE === "true") {
    return next();
  }

  // Get the RapidAPI proxy secret header
  const proxySecret = req.headers["x-rapidapi-proxy-secret"];

  // Validate the proxy secret
  const isValidSecret = proxySecret === RAPIDAPI_PROXY_SECRET;

  // If secret is invalid, return 403 Forbidden
  if (!isValidSecret) {
    return res.status(403).json({ error: "Access forbidden" });
  }

  next();
};


const together = new Together({
  apiKey: process.env.TOGETHER_API_KEY || "bdceafda18ca965c9eb0a060a6d48e680a5cf85e36eac53ec1714179f78f1a81"
});

const modelMap = {
  "Llama-3.3-70B-Instruct-Turbo": {
    id: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
    description: "Meta's 70B parameter Llama 3 model (turbo version)"
  },
  "DeepSeek-R1-Distill-Llama-70B": {
    id: "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free",
    description: "DeepSeek's distilled version of Llama 70B"
  },
  "EXAONE-3.5-32B-Instruct": {
    id: "lgai/exaone-3-5-32b-instruct",
    description: "EXAONE"
  },
  "EXAONE-Deep-32B": {
    id: "lgai/exaone-deep-32b",
    description: "EXAONE"
  },
  "AFM-4.5B-Preview": {
    id: "arcee-ai/AFM-4.5B-Preview",
    description: "AFM"
  },
};
function generateDeviceId() {
  const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
  const numbers = "**********";
  const randomNumbers = Array.from({ length: 8 }, () => numbers[Math.floor(Math.random() * numbers.length)]).join("");
  return `dev${randomNumbers}`;
}

function getRandomSeed() {
  return Math.floor(Math.random() * 1000000);
}

// Simple ping endpoint for health checks
app.get("/ping", async (req, res) => {
  res.status(200).json({ message: "pong" });
});

// Debug endpoint to test RapidAPI request parsing (no auth required)
app.post("/debug-request", async (req, res) => {
  console.log('=== DEBUG ENDPOINT ===');
  console.log('Headers:', JSON.stringify(req.headers, null, 2));
  console.log('Raw body:', req.body);
  console.log('Body type:', typeof req.body);
  console.log('Body keys:', req.body ? Object.keys(req.body) : 'No body');
  console.log('Content-Type:', req.headers['content-type']);
  console.log('=====================');

  res.json({
    received_headers: req.headers,
    received_body: req.body,
    body_type: typeof req.body,
    body_keys: req.body ? Object.keys(req.body) : null,
    content_type: req.headers['content-type']
  });
});


const PLATFORMS = {
  twitter: (u) => `https://twitter.com/${u}`,
  github: (u) => `https://github.com/${u}`,
  reddit: (u) => `https://www.reddit.com/user/${u}`,
  instagram: (u) => `https://www.instagram.com/${u}`,
  youtube: (u) => `https://www.youtube.com/@${u}`,
  facebook: (u) => `https://www.facebook.com/${u}`,
  tiktok: (u) => `https://www.tiktok.com/@${u}`,
  pinterest: (u) => `https://www.pinterest.com/${u}`,
  twitch: (u) => `https://www.twitch.tv/${u}`,
  threads: (u) => `https://www.threads.net/@${u}`,
  snapchat: (u) => `https://www.snapchat.com/add/${u}`,
  discord: (u) => `https://discord.com/users/${u}`,
  telegram: (u) => `https://t.me/${u}`,
  whatsapp: (u) => `https://wa.me/${u}`,
  linkedin: (u) => `https://www.linkedin.com/in/${u}`,
  mastodon: (u) => `https://mastodon.social/@${u}`,
  bluesky: (u) => `https://bsky.app/profile/${u}`,
  substack: (u) => `https://${u}.substack.com`,
  medium: (u) => `https://medium.com/@${u}`,
  devto: (u) => `https://dev.to/${u}`,
  codepen: (u) => `https://codepen.io/${u}`,
  replit: (u) => `https://replit.com/@${u}`,
  hackernews: (u) => `https://news.ycombinator.com/user?id=${u}`,
  producthunt: (u) => `https://www.producthunt.com/@${u}`,
  gumroad: (u) => `https://gumroad.com/${u}`,
  ko_fi: (u) => `https://ko-fi.com/${u}`,
  buymeacoffee: (u) => `https://www.buymeacoffee.com/${u}`,
  soundcloud: (u) => `https://soundcloud.com/${u}`,
  bandcamp: (u) => `https://${u}.bandcamp.com`,
  mewe: (u) => `https://mewe.com/i/${u}`,
  minds: (u) => `https://www.minds.com/${u}`,
  yubo: (u) => `https://yubo.live/${u}`,
  spacehey: (u) => `https://spacehey.com/${u}`,
  nourl: (u) => `https://noplace.com/${u}`,
  echo_space: (u) => `https://echospace.app/${u}`,
  lapsen: (u) => `https://lapse.com/${u}`,
  dispost: (u) => `https://dispo.inc/@${u}`,
  be_real: (u) => `https://bere.al/${u}`,
  artifact: (u) => `https://artifact.com/u/${u}`,
  strap_circle: (u) => `https://loopcircle.app/u/${u}`,
  komyun: (u) => `https://komyun.app/${u}`,
  hivecast: (u) => `https://hivecast.com/${u}`,
  fableworld: (u) => `https://fableworld.com/${u}`,
  viim: (u) => `https://viim.app/${u}`,
  zentry: (u) => `https://zentry.app/${u}`,
  clubhouse: (u) => `https://www.clubhouse.com/@${u}`,
  nextdoor: (u) => `https://nextdoor.com/profile/${u}`,
  goodreads: (u) => `https://www.goodreads.com/user/show/${u}`,
};


app.get("/check", async (req, res) => {
  const username = req.query.username;
  if (!username) {
    return res.status(400).json({ error: "Missing 'username' parameter" });
  }

  const results = {};

  await Promise.all(
    Object.entries(PLATFORMS).map(async ([platform, buildUrl]) => {
      const url = buildUrl(username);
      try {
        const response = await fetch(url, { method: "HEAD" });
        results[platform] = {
          exists: response.ok,
          url: response.ok ? url : null,
        };
      } catch {
        results[platform] = {
          exists: false,
          url: null,
        };
      }
    })
  );

  res.json({
    username,
    platforms: results,
    timestamp: Date.now(),
  });
});


app.get("/Geo-detect", async (req, res) => {
  // 1. Client IP (behind proxies too)
  const ip = req.ip || req.headers["x-forwarded-for"]?.split(",")[0] || "";

  // 2. Device/User-Agent parsing
  const uaString = req.headers["user-agent"] || "";
  const parser = new UAParser(uaString);
  const uaResult = parser.getResult();

  // 3. Geolocation API
  let geo = {};
  try {
    const geoRes = await fetch(`https://ipapi.co/${ip}/json/`);
    geo = await geoRes.json();
  } catch {
    geo = { error: "Geo lookup failed" };
  }

  res.json({
    ip,
    geo,
    device: {
      browser: uaResult.browser.name,
      browserVersion: uaResult.browser.version,
      os: uaResult.os.name,
      osVersion: uaResult.os.version,
      device: uaResult.device.model || "unknown",
      type: uaResult.device.type || "desktop",
      engine: uaResult.engine.name
    },
    headers: {
      "accept-language": req.headers["accept-language"],
      referer: req.headers["referer"],
      "user-agent": uaString
    },
    timestamp: Date.now()
  });
});


// =====================
// ADDED TRANSLATION ENDPOINT
// =====================
app.get("/translate", async (req, res) => {
  const text = req.query.text;
  const lang = req.query.lang;

  // Validate parameters
  if (!text || !lang) {
    return res.status(400).json({
      error: "Both 'text' and 'lang' parameters are required",
      example: "/translate?text=Hello%20World&lang=Spanish"
    });
  }

  // Decode and sanitize inputs
  const decodedText = decodeURIComponent(text).slice(0, 500);
  const decodedLang = decodeURIComponent(lang).slice(0, 50);

  try {
    // Use Together AI for translation
    const response = await together.chat.completions.create({
      model: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
      messages: [
        {
          role: "system",
          content: `Translate the text to ${decodedLang} without explanations. Only return the translated text.`
        },
        {
          role: "user",
          content: `Translate: "${decodedText}"`
        }
      ],
      temperature: 0.3,
      max_tokens: 500,
    });

    const translation = response.choices[0]?.message?.content?.trim() || "";

    res.json({
      original: decodedText,
      translation,
      language: decodedLang,
      model: "Llama-3.3-70B-Instruct-Turbo",
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Translation error:", error);
    res.status(500).json({
      error: "Failed to translate text",
      details: error.message
    });
  }
});

// LLM - Session management removed, using stateless POST requests

// POST /v1/chat/completions
// OpenAI-compatible chat completions endpoint with streaming support
app.post("/v1/chat/completions", validateRapidAPIRequest, async (req, res) => {
  // Debug logging for RapidAPI troubleshooting
  console.log('=== DEBUG INFO ===');
  console.log('Headers:', JSON.stringify(req.headers, null, 2));
  console.log('Raw body:', req.body);
  console.log('Body type:', typeof req.body);
  console.log('Body keys:', req.body ? Object.keys(req.body) : 'No body');
  console.log('==================');

  // Get request body parameters
  const {
    model,
    messages = [],
    max_tokens = 512,
    temperature = 0.7,
    stream = false
  } = req.body;

  // Validate required parameters
  if (!model || !messages || !Array.isArray(messages) || messages.length === 0) {
    return res.status(400).json({
      error: {
        message: "Missing required parameters",
        type: "invalid_request_error",
        param: !model ? "model" : "messages",
        code: "missing_required_parameter"
      }
    });
  }

  // Find model info
  const modelInfo = modelMap[model];
  if (!modelInfo) {
    return res.status(400).json({
      error: {
        message: `Model '${model}' not found`,
        type: "invalid_request_error",
        param: "model",
        code: "model_not_found"
      },
      available_models: Object.keys(modelMap)
    });
  }

  // Validate message format
  for (let i = 0; i < messages.length; i++) {
    const message = messages[i];
    if (!message.role || !message.content) {
      return res.status(400).json({
        error: {
          message: `Invalid message format at index ${i}. Each message must have 'role' and 'content' fields`,
          type: "invalid_request_error",
          param: `messages[${i}]`,
          code: "invalid_message_format"
        }
      });
    }

    if (!["system", "user", "assistant"].includes(message.role)) {
      return res.status(400).json({
        error: {
          message: `Invalid role '${message.role}' at message index ${i}. Must be 'system', 'user', or 'assistant'`,
          type: "invalid_request_error",
          param: `messages[${i}].role`,
          code: "invalid_role"
        }
      });
    }
  }

  try {
    // Use messages directly from the request

    // Call Together AI API
    if (stream) {
      // Set headers for streaming
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

      // Send initial metadata
      res.write(`data: ${JSON.stringify({
        type: 'metadata',
        model: model,
        timestamp: new Date().toISOString()
      })}\n\n`);

      let fullContent = '';

      try {
        const streamResponse = await together.chat.completions.create({
          messages: messages,
          model: modelInfo.id,
          max_tokens: max_tokens,
          temperature: temperature,
          stream: true
        });

        for await (const chunk of streamResponse) {
          const content = chunk.choices[0]?.delta?.content || '';
          if (content) {
            fullContent += content;
            res.write(`data: ${JSON.stringify({
              type: 'content',
              content: content,
              timestamp: new Date().toISOString()
            })}\n\n`);
          }
        }

        // Send completion signal
        res.write(`data: ${JSON.stringify({
          type: 'done',
          full_content: fullContent,
          timestamp: new Date().toISOString()
        })}\n\n`);

        res.end();
      } catch (streamError) {
        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: streamError.message,
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    } else {
      // Non-streaming response
      const response = await together.chat.completions.create({
        messages: messages,
        model: modelInfo.id,
        max_tokens: max_tokens,
        temperature: temperature,
        stream: false
      });

      const completion = response.choices[0]?.message;
      const usage = response.usage;

      // Prepare OpenAI-compatible response
      res.json({
        id: `chatcmpl-${crypto.randomUUID()}`,
        object: "chat.completion",
        created: Math.floor(Date.now() / 1000),
        model: model,
        choices: [{
          index: 0,
          message: {
            role: "assistant",
            content: completion?.content || ""
          },
          finish_reason: response.choices[0]?.finish_reason || "stop"
        }],
        usage: {
          prompt_tokens: usage?.prompt_tokens || 0,
          completion_tokens: usage?.completion_tokens || 0,
          total_tokens: usage?.total_tokens || 0
        }
      });
    }

  } catch (error) {
    console.error("API error:", error);
    res.status(500).json({
      error: {
        message: "Failed to process request",
        type: "api_error",
        code: "internal_error",
        details: error.message
      }
    });
  }
});

app.get("/tts", async (req, res) => {
  const content = req.query.content;
  const voice_id = req.query.voice_id || "af_bella";
  const category = req.query.category || "en";

  if (!content) {
    return res.status(400).json({ error: "Missing required parameter: content" });
  }

  try {
    // Step 1: Initial TTS Request
    const initRes = await fetch("https://vchanger.ai/api/v1/ai-text-to-audio/audio", {
      method: "POST",
      headers: {
        "accept": "application/json, text/plain, */*",
        "content-type": "application/json",
        "origin": "https://vchanger.ai",
        "referer": "https://vchanger.ai/text-to-speech",
        "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
      },
      body: JSON.stringify({
        content,
        voice_id,
        category
      })
    });

    const initData = await initRes.json();
    const audio_id = initData?.data?.audio_id;

    if (!audio_id) {
      ctx.response.status = 500;
      ctx.response.body = { error: "Failed to create TTS task" };
      return;
    }

    // Step 2: Poll for result
    let resultData;
    for (let i = 0; i < 10; i++) {
      await new Promise((res) => setTimeout(res, 2000));
      const pollRes = await fetch(`https://vchanger.ai/api/v1/ai-text-to-audio/audio/${audio_id}`);
      const pollData = await pollRes.json();

      if (pollData?.data?.status === "success" && pollData?.data?.audio_url) {
        resultData = pollData.data;
        break;
      }
    }

    if (!resultData?.audio_url) {
      return res.status(504).json({ error: "TTS generation timed out or failed" });
    }

    // Replace audio domain
    const modified_url = resultData.audio_url.replace(
      "https://cdn.vchanger.ai/vchanger/products/audio/",
      "https://cdn.picgenv.net/tts/"
    );

    // Respond with custom audio URL
    res.json({
      status: "success",
      voice_id,
      category,
      content,
      audio_url: modified_url,
      timestamp: Date.now()
    });
  } catch (err) {
    console.error("TTS Error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});



// Note: Session management removed - use messages array in POST requests for conversation history

// Add a health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    storage: "In-Memory",
    models_available: Object.keys(modelMap).length
  });
});
// Add a models list endpoint
app.get("/v1/models", (req, res) => {
  res.json({
    models: Object.entries(modelMap).map(([alias, info]) => ({
      alias,
      id: info.id,
      description: info.description
    }))
  });
});


const userAgents = [
  // Windows desktop
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 6.3; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 6.4; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/145.0.0.0 Safari/537.36",
  "Mozilla/5.0 (Windows NT 6.5; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/146.0.0.0 Safari/537.36",

  // Mac desktop
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_3_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_6_8) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_4) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.5 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_7_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.2.1 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_5_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6.1 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_8) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.3 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.7 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_9) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.4 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 12_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.8 Safari/605.1.15",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_10) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.5 Safari/605.1.15",

  // Linux desktop
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",

  // iPhone
  "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16A366",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16B92",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16C101",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16D39",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPhone; CPU iPhone OS 16_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16E226",

  // iPad
  "Mozilla/5.0 (iPad; CPU OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPad; CPU OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16A366",
  "Mozilla/5.0 (iPad; CPU OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
  "Mozilla/5.0 (iPad; CPU OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16B92",
  "Mozilla/5.0 (iPad; CPU OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16B95",
// iPad
"Mozilla/5.0 (iPad; CPU OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
"Mozilla/5.0 (iPad; CPU OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16A366",
"Mozilla/5.0 (iPad; CPU OS 15_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
"Mozilla/5.0 (iPad; CPU OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16B92",
"Mozilla/5.0 (iPad; CPU OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
"Mozilla/5.0 (iPad; CPU OS 16_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16C101",
"Mozilla/5.0 (iPad; CPU OS 15_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
"Mozilla/5.0 (iPad; CPU OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16D39",
"Mozilla/5.0 (iPad; CPU OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
"Mozilla/5.0 (iPad; CPU OS 16_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16E226",

// Android phone
"Mozilla/5.0 (Linux; Android 11; SM-G960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-G970U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-G981U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-G960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-G985U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-G970U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-G981U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-G960U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-G985U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-G970U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",

// Android tablet
"Mozilla/5.0 (Linux; Android 11; SM-T970) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-T960) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-T981) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-T970) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-T985) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-T960) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-T981) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-T970) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 11; SM-T985) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",
"Mozilla/5.0 (Linux; Android 10; SM-T960) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Safari/537.36",

// Windows phone
"Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 950) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263",
"Mozilla/5.0 (Windows Phone 8.1; Android 4.4.2; Nokia; Lumia 635) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/12.0",
"Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 950 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263",
"Mozilla/5.0 (Windows Phone 8.1; Android 4.4.2; Nokia; Lumia 735) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/12.0",
"Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 650) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263",
"Mozilla/5.0 (Windows Phone 8.1; Android 4.4.2; Nokia; Lumia 830) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/12.0",
"Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 950) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263",
"Mozilla/5.0 (Windows Phone 8.1; Android 4.4.2; Nokia; Lumia 635) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/12.0",
"Mozilla/5.0 (Windows Phone 10.0; Android 6.0.1; Microsoft; Lumia 950 XL) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/14.14263",
"Mozilla/5.0 (Windows Phone 8.1; Android 4.4.2; Nokia; Lumia 735) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Mobile Safari/537.36 Edge/12.0",
];

// Random sec-ch-ua generators
function getRandomSecChUa() {
  return `"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"`;
}

// Supported models for the custom endpoint
const SUPPORTED_CUSTOM_MODELS = [
  'grok-3-mini',
  'gemini-2.5-flash',
  'gpt-4.1-nano',
  'gpt-4.1-mini'
];

// Function to generate a nodeId (UUID v4 format)
function generateNodeId() {
  return crypto.randomUUID();
}

// Function to attempt to get nodeId from OPTIONS request (fallback to generated UUID)
async function getNodeId() {
  try {
    // Try to get nodeId from OPTIONS request
    const optionsResponse = await fetch("https://edge.flowith.net/ai/chat?mode=general", {
      method: "OPTIONS",
      headers: {
        "accept": "*/*",
        "accept-language": "en-US,en;q=0.9",
        "access-control-request-headers": "authorization,content-type,responsetype",
        "access-control-request-method": "POST",
        "origin": "https://flowith.net",
        "referer": "https://flowith.net/",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
      }
    });

    // Check if there's a nodeId in response headers or body
    const nodeIdHeader = optionsResponse.headers.get('x-node-id') ||
                        optionsResponse.headers.get('node-id') ||
                        optionsResponse.headers.get('x-session-id');

    if (nodeIdHeader) {
      return nodeIdHeader;
    }

    // If no nodeId found in OPTIONS response, generate one
    return generateNodeId();
  } catch (error) {
    // Fallback to generated UUID if OPTIONS request fails
    console.log('OPTIONS request failed, using generated nodeId:', error.message);
    return generateNodeId();
  }
}

// Route Handler
app.get("/custom/:model", async (req, res) => {
  try {
    const model = req.params.model;
    const prompt = req.query.prompt;
    const system = req.query.system;
    const stream = req.query.stream === "true";

    if (!prompt || !model) {
      return res.status(400).json({
        error: "Missing required parameters",
        required: ["model (in path)", "prompt (query parameter)"],
        example: "/custom/grok-3-mini?prompt=Hello+world&system=You+are+helpful+assistant&stream=true",
        supported_models: SUPPORTED_CUSTOM_MODELS
      });
    }

    // Validate model
    if (!SUPPORTED_CUSTOM_MODELS.includes(model)) {
      return res.status(400).json({
        error: `Model '${model}' is not supported`,
        supported_models: SUPPORTED_CUSTOM_MODELS,
        example: "/custom/grok-3-mini?prompt=Hello+world&system=You+are+helpful+assistant"
      });
    }

    // Build messages array
    const messages = [];
    if (system) {
      messages.push({ role: "system", content: system });
    }
    messages.push({ role: "user", content: prompt });

    // Get nodeId for the request
    const nodeId = await getNodeId();

    // Use the exact headers from the working curl example
    const response = await fetch("https://edge.flowith.net/ai/chat?mode=general", {
      method: "POST",
      headers: {
        "accept": "*/*",
        "accept-language": "en-US,en;q=0.9",
        "authorization": "",
        "content-type": "application/json",
        "origin": "https://flowith.net",
        "priority": "u=1, i",
        "referer": "https://flowith.net/",
        "responsetype": "stream",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "sec-gpc": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
      },
      body: JSON.stringify({
        model,
        messages,
        stream: stream,
        nodeId: nodeId
      })
    });

    if (stream) {
      // Handle streaming response
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

      // Send initial metadata
      res.write(`data: ${JSON.stringify({
        type: 'metadata',
        model: model,
        nodeId: nodeId,
        timestamp: new Date().toISOString()
      })}\n\n`);

      if (response.body) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let fullContent = '';

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data.trim() && data !== '[DONE]') {
                  try {
                    const parsed = JSON.parse(data);
                    // Handle different possible response formats from Flowith
                    const content = parsed.choices?.[0]?.delta?.content ||
                                  parsed.delta?.content ||
                                  parsed.content ||
                                  parsed.text || '';

                    if (content) {
                      fullContent += content;
                      res.write(`data: ${JSON.stringify({
                        type: 'content',
                        content: content,
                        timestamp: new Date().toISOString()
                      })}\n\n`);
                    }
                  } catch (e) {
                    // Skip invalid JSON
                    console.log('Failed to parse streaming chunk:', data);
                  }
                }
              }
            }
          }

          res.write(`data: ${JSON.stringify({
            type: 'done',
            full_content: fullContent,
            timestamp: new Date().toISOString()
          })}\n\n`);
          res.end();
        } catch (streamError) {
          console.error('Streaming error:', streamError);
          res.write(`data: ${JSON.stringify({
            type: 'error',
            error: streamError.message,
            timestamp: new Date().toISOString()
          })}\n\n`);
          res.end();
        }
      } else {
        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: 'No response body from Flowith API',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    } else {
      // Non-streaming response
      if (!response.ok) {
        throw new Error(`Flowith API error: ${response.status} ${response.statusText}`);
      }

      const contentType = response.headers.get("content-type") || "";
      let data;

      if (contentType.includes("application/json")) {
        const jsonResponse = await response.json();

        // Handle Flowith API response format
        const content = jsonResponse.choices?.[0]?.message?.content ||
                       jsonResponse.content ||
                       jsonResponse.text ||
                       jsonResponse.response ||
                       "No content received";

        data = {
          id: `custom-${Date.now()}`,
          object: "chat.completion",
          created: Math.floor(Date.now() / 1000),
          model,
          choices: [{
            index: 0,
            message: {
              role: "assistant",
              content: content
            },
            finish_reason: jsonResponse.choices?.[0]?.finish_reason || "stop"
          }],
          usage: jsonResponse.usage || {
            prompt_tokens: 0,
            completion_tokens: 0,
            total_tokens: 0
          },
          nodeId: nodeId
        };
      } else {
        // Handle text response
        const textResponse = await response.text();
        let content = textResponse.trim();

        // Handle special Flowith response formats
        if (textResponse.includes("<say>")) {
          const sayMatches = textResponse.match(/<say>([\s\S]*?)<\/say>/g) || [];
          const contents = sayMatches.map(m => m.replace(/<\/?say>/g, "").trim());
          content = contents.join("\n\n").trim();
        }

        data = {
          id: `custom-${Date.now()}`,
          object: "chat.completion",
          created: Math.floor(Date.now() / 1000),
          model,
          choices: [{
            index: 0,
            message: {
              role: "assistant",
              content: content || "Response received"
            },
            finish_reason: "stop"
          }],
          nodeId: nodeId
        };
      }

      res.json(data);
    }

  } catch (error) {
    console.error("Custom model error:", error);
    res.status(500).json({
      error: {
        message: "Failed to process request with Flowith API",
        type: "api_error",
        code: "flowith_error",
        details: error.message
      },
      troubleshooting: [
        "Try a different supported model",
        "Simplify your prompt",
        "Check Flowith API availability",
        "Add system prompt for better guidance",
        "Verify model name is correct"
      ],
      supported_models: SUPPORTED_CUSTOM_MODELS,
      api_provider: "Flowith (edge.flowith.net)"
    });
  }
});

// OpenAI-compatible POST endpoint for custom models
app.post("/v1/chat/completions/custom", validateRapidAPIRequest, async (req, res) => {
  try {
    // Debug logging for RapidAPI troubleshooting
    console.log('=== CUSTOM DEBUG INFO ===');
    console.log('Headers:', JSON.stringify(req.headers, null, 2));
    console.log('Raw body:', req.body);
    console.log('Body type:', typeof req.body);
    console.log('Body keys:', req.body ? Object.keys(req.body) : 'No body');
    console.log('========================');

    const {
      model,
      messages = [],
      stream = false,
      max_tokens = 512,
      temperature = 0.7
    } = req.body;

    // Validate required parameters
    if (!model || !messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({
        error: {
          message: "Missing required parameters",
          type: "invalid_request_error",
          param: !model ? "model" : "messages",
          code: "missing_required_parameter"
        }
      });
    }

    // Validate model
    if (!SUPPORTED_CUSTOM_MODELS.includes(model)) {
      return res.status(400).json({
        error: {
          message: `Model '${model}' is not supported`,
          type: "invalid_request_error",
          param: "model",
          code: "model_not_found"
        },
        supported_models: SUPPORTED_CUSTOM_MODELS
      });
    }

    // Get nodeId for the request
    const nodeId = await getNodeId();

    // Call Flowith API
    const response = await fetch("https://edge.flowith.net/ai/chat?mode=general", {
      method: "POST",
      headers: {
        "accept": "*/*",
        "accept-language": "en-US,en;q=0.9",
        "authorization": "",
        "content-type": "application/json",
        "origin": "https://flowith.net",
        "priority": "u=1, i",
        "referer": "https://flowith.net/",
        "responsetype": "stream",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "sec-gpc": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
      },
      body: JSON.stringify({
        model,
        messages,
        stream: stream,
        nodeId: nodeId
      })
    });

    if (!response.ok) {
      throw new Error(`error: ${response.status} ${response.statusText}`);
    }

    if (stream) {
      // Handle streaming response (similar to the GET endpoint)
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullContent = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data.trim() && data !== '[DONE]') {
                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices?.[0]?.delta?.content ||
                                parsed.delta?.content ||
                                parsed.content ||
                                parsed.text || '';

                  if (content) {
                    fullContent += content;
                    // Send OpenAI-compatible streaming format
                    res.write(`data: ${JSON.stringify({
                      id: `chatcmpl-${Date.now()}`,
                      object: "chat.completion.chunk",
                      created: Math.floor(Date.now() / 1000),
                      model: model,
                      choices: [{
                        index: 0,
                        delta: {
                          content: content
                        },
                        finish_reason: null
                      }]
                    })}\n\n`);
                  }
                } catch (e) {
                  console.log('Failed to parse streaming chunk:', data);
                }
              }
            }
          }
        }

        // Send final chunk
        res.write(`data: ${JSON.stringify({
          id: `chatcmpl-${Date.now()}`,
          object: "chat.completion.chunk",
          created: Math.floor(Date.now() / 1000),
          model: model,
          choices: [{
            index: 0,
            delta: {},
            finish_reason: "stop"
          }]
        })}\n\n`);
        res.write('data: [DONE]\n\n');
        res.end();
      } catch (streamError) {
        console.error('Streaming error:', streamError);
        res.write(`data: ${JSON.stringify({
          error: {
            message: streamError.message,
            type: "api_error"
          }
        })}\n\n`);
        res.end();
      }
    } else {
      // Non-streaming response
      const contentType = response.headers.get("content-type") || "";
      let content = "No content received";

      try {
        if (contentType.includes("application/json")) {
          const jsonResponse = await response.json();
          content = jsonResponse.choices?.[0]?.message?.content ||
                   jsonResponse.content ||
                   jsonResponse.text ||
                   jsonResponse.response ||
                   "No content received";
        } else {
          // Handle text response
          const textResponse = await response.text();
          content = textResponse.trim();

          // Handle special Flowith response formats
          if (textResponse.includes("<say>")) {
            const sayMatches = textResponse.match(/<say>([\s\S]*?)<\/say>/g) || [];
            const contents = sayMatches.map(m => m.replace(/<\/?say>/g, "").trim());
            content = contents.join("\n\n").trim();
          }
        }
      } catch (parseError) {
        console.error('Failed to parse response:', parseError);
        content = "Failed to parse response from Flowith API";
      }

      res.json({
        id: `chatcmpl-${Date.now()}`,
        object: "chat.completion",
        created: Math.floor(Date.now() / 1000),
        model: model,
        choices: [{
          index: 0,
          message: {
            role: "assistant",
            content: content
          },
          finish_reason: "stop"
        }],
        usage: {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0
        }
      });
    }

  } catch (error) {
    console.error("Custom model POST error:", error);
    res.status(500).json({
      error: {
        message: "Failed to process request with Flowith API",
        type: "api_error",
        code: "flowith_error",
        details: error.message
      }
    });
  }
});

// API routes
app.get("/create-v4", async (req, res) => {
  try {
    let prompt = req.query.prompt;
    const size = req.query.size || "1024x1024";
    const style = (req.query.style || "default").toLowerCase();

    if (!prompt) {
      return res.status(400).json({ error: "Missing required parameter: prompt" });
    }

    const sizeMatch = size.match(/^(\d{2,4})x(\d{2,4})$/);
    if (!sizeMatch) {
      return res.status(400).json({ error: "Invalid size format. Use WIDTHxHEIGHT like 1024x1024" });
    }

    const width = parseInt(sizeMatch[1]);
    const height = parseInt(sizeMatch[2]);

    // Style prompt mapping
    const styleMap = {
      default: "",
      cyberpunk: "ultra-detailed futuristic cyberpunk cityscape, neon lights, high-tech aesthetics, Blade Runner vibe",
      anime: "vibrant anime illustration, expressive characters, large eyes, Japanese art style, Studio Ghibli influence",
      pixelart: "8-bit retro pixel art style, low-resolution, vibrant color blocks, nostalgic video game aesthetic",
      oilpaint: "realistic classic oil painting, rich textures, Renaissance style, dramatic lighting, brushstroke details",
      "3d": "highly detailed 3D render, digital sculpting, photorealistic textures, studio lighting, cinematic angle"
    };


    if (!(style in styleMap)) {
      return res.status(400).json({ error: "Invalid style. Available: default, cyberpunk, anime, pixelart, oilpaint, 3d" });
    }

    // Append style if any
    const stylePrompt = styleMap[style];
    if (stylePrompt) prompt += `, ${stylePrompt}`;

    const seed = Math.floor(Math.random() * 1000000000);
    const encodedPrompt = encodeURIComponent(prompt);
    const imageUrl = `https://fluxwebui.com/generate/${encodedPrompt}?width=${width}&height=${height}&seed=${seed}&model=flux&nologo=true&nofeed=true`;

    const imageRes = await fetch(imageUrl, {
      headers: {
        "accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
        "accept-language": "en-US,en;q=0.9",
        "referer": "https://fluxwebui.com/tools/ai-image-generator",
        "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "image",
        "sec-fetch-mode": "no-cors",
        "sec-fetch-site": "same-origin",
        "sec-gpc": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
      }
    });

    if (!imageRes.ok) {
      return res.status(500).json({ error: "Failed to generate image" });
    }

    const buffer = await imageRes.arrayBuffer();
    res.status(200);
    res.set("Content-Type", "image/png");
    res.send(Buffer.from(buffer));
  } catch (err) {
    console.error("Error in /create-v4:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});


app.get("/create-v1", async (req, res) => {
  try {
    const prompt = req.query.prompt;
    const size = req.query.size || "1024x1024";
    const style = req.query.style || "Default";
    const device_id = generateDeviceId();
    const model = "flux-1-dev";

    if (!prompt) {
      return res.status(400).json({ error: "Missing prompt parameter." });
    }

    const generationRes = await fetch(`https://api-preview.apirouter.ai/api/v1/deepimg/${model}`, {
      method: "POST",
      headers: {
        "accept": "*/*",
        "content-type": "application/json",
        "origin": "https://deepimg.ai",
        "referer": "https://deepimg.ai/",
        "user-agent": "Mozilla/5.0",
      },
      body: JSON.stringify({
        device_id,
        prompt: `${prompt} -style ${style}`,
        size,
        n: "1",
        output_format: "png",
      }),
    });

    const result = await generationRes.json();

    if (result?.code !== 0 || !result?.data?.images?.length) {
      return res.status(500).json({
        code: result?.code ?? 500,
        message: "Failed to generate image.",
        data: null,
      });
    }

    // Transform image URLs from middle.ihomepage.app to cdn.picgenv.net/fluxai/
    const transformedImages = result.data.images.map(img => {
      if (typeof img === 'string') {
        return img.replace('https://middle.ihomepage.app', 'https://cdn.picgenv.net/fluxai');
      } else if (img.url) {
        return {
          ...img,
          url: img.url.replace('https://middle.ihomepage.app', 'https://cdn.picgenv.net/fluxai')
        };
      }
      return img;
    });

    res.status(200).json({
      code: 0,
      message: "Success",
      data: {
        api: "https://rapidapi.com/FreeCode911/api/flux-api-4-custom-models-100-style",
        prompt,
        size,
        style,
        model,
        images: transformedImages.map(img => {
          if (typeof img === 'string') {
            return { url: img };
          } else if (img.url) {
            return { url: img.url };
          }
          return img;
        })
      },
      timestamp: Math.floor(Date.now() / 1000),
    });
  } catch (error) {
    console.error("Error in create-v1 route:", error);
    res.status(500).json({
      code: 500,
      message: "Internal server error.",
      data: null,
    });
  }
});

app.get("/create-v2", async (req, res) => {
  try {
    const prompt = req.query.prompt;
    const aspect_ratio = req.query.aspect_ratio;
    if (!prompt || !aspect_ratio) {
      return res.status(400).json({ error: "Missing required parameters: prompt or aspect_ratio( 1:1 , 2:3 , 3:2 )" });
    }

    const apiUrl = `https://1yjs1yldj7.execute-api.us-east-1.amazonaws.com/default/ai_image?prompt=${encodeURIComponent(prompt)}&aspect_ratio=${encodeURIComponent(aspect_ratio)}&link=writecream.com`;

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Accept": "application/json, text/plain, */*",
        "User-Agent": "Mozilla/5.0",
      },
    });

    if (!response.ok) {
      return res.status(response.status).json({ error: "AI processing failed" });
    }

    const data = await response.json();

    // Transform the image URL from dbuzz-assets.s3.amazonaws.com to cdn.picgenv.net/fluxai2/
    let imageLink = data.image_link || "";
    imageLink = imageLink.replace("https://dbuzz-assets.s3.amazonaws.com/ai_image/public", "https://cdn.picgenv.net/fluxai2");

    res.json({
      status: data.status || "success",
      model: "flux shell fast",
      aspect_ratio,
      api: "https://rapidapi.com/FreeCode911/api/flux-api-4-custom-models-100-style",
      image_link: imageLink
    });
  } catch (error) {
    console.error("Error in create-v2 route:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.get("/create-v3", async (req, res) => {
  try {
    const prompt = req.query.prompt;
    const width = req.query.width || "384";
    const height = req.query.height || "384";
    const seed = req.query.seed || getRandomSeed();

    if (!prompt) {
      return res.status(400).json({ error: "Missing required parameter: prompt" });
    }

    const apiUrl = `https://image.pollinations.ai/prompt/${encodeURIComponent(prompt)}?width=${width}&height=${height}&model=flux&seed=${seed}&nologo=true&enhance=false`;

    const response = await fetch(apiUrl);

    if (!response.ok) {
      return res.status(response.status).json({ error: "AI processing failed" });
    }

    const imageBuffer = await response.arrayBuffer();

    res.status(200);
    res.set("Content-Type", "image/png");
    res.send(Buffer.from(imageBuffer));
  } catch (error) {
    console.error("Error in create-v3 route:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.get("/create-v1.1-text-to-video", async (req, res) => {
  try {
    const prompt = req.query.prompt;
    const user_id = req.query.user_id || "1";
    const steps = req.query.steps || "50";

    if (!prompt) {
      return res.status(400).json({ error: "Missing required parameter: prompt" });
    }

    const apiUrl = "https://shorts.multiplewords.com/mwvideos/api/text_to_video";

    const formData = new FormData();
    formData.append("user_id", user_id);
    formData.append("prompt", prompt);
    formData.append("steps", steps);

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Accept": "*/*",
        "User-Agent": "Mozilla/5.0",
      },
      body: formData,
    });

    const data = await response.json();

    if (data.status !== 1 || !data.video_url) {
      return res.status(500).json({ error: "Failed to retrieve video URL" });
    }

    console.log("Original video_url:", data.video_url);

    // Replace only the domain part dynamically
    const customVideoUrl = data.video_url.replace(
      /^https:\/\/[^/]+/,
      "https://cdn.picgenv.net"
    );

    res.json({
      status: "success",
      video_url: customVideoUrl,
    });
  } catch (error) {
    console.error("Error in create-v1.1-text-to-video route:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

app.get("/img2img", async (req, res) => {
  try {
    const image = req.query.image;
    const prompt = req.query.prompt;
    const aspect_ratio = req.query.aspect_ratio || "1:1";
    const device_id = generateDeviceId(); // Your helper function

    if (!image || !prompt) {
      return res.status(400).json({
        error: "Missing required parameters: image and prompt",
        example: "/img2img/kling?image=https://...&prompt=a+new+style"
      });
    }

    // Submit the task
    const submitRes = await fetch("https://api-preview.apirouter.ai/api/v1/deepimg/kling", {
      method: "POST",
      headers: {
        "accept": "*/*",
        "content-type": "application/json",
        "origin": "https://deepimg.ai",
        "referer": "https://deepimg.ai/",
        "user-agent": "Mozilla/5.0",
      },
      body: JSON.stringify({
        image,
        prompt,
        aspect_ratio,
        device_id
      }),
    });

    const submitData = await submitRes.json();

    if (submitData.code !== 0 || !submitData.data?.task_id) {
      return res.status(500).json({
        error: "Image-to-image task submission failed",
        details: submitData
      });
    }

    const task_id = submitData.data.task_id;
    const pollUrl = `https://api-preview.apirouter.ai/api/v1/images/query?task_id=${task_id}&model=kling-v1&device_id=${device_id}`;

    // Polling function
    const pollForResult = async (retries = 30, delay = 2000) => {
      for (let i = 0; i < retries; i++) {
        const pollRes = await fetch(pollUrl, {
          headers: {
            "accept": "*/*",
            "origin": "https://deepimg.ai",
            "referer": "https://deepimg.ai/",
            "user-agent": "Mozilla/5.0",
          },
        });

        const pollData = await pollRes.json();

        if (pollData?.data?.status === "completed" && pollData.data.success) {
          return pollData.data.images.map(img => ({
            url: img.url.replace("https://middle.ihomepage.app", "https://cdn.picgenv.net/fluxai"),
          }));
        }

        await new Promise(resolve => setTimeout(resolve, delay));
      }

      throw new Error("Image generation timed out. Try again later.");
    };

    const finalImages = await pollForResult();

    res.status(200).json({
      status: "completed",
      model: "kling-v1",
      prompt,
      aspect_ratio,
      images: finalImages,
      timestamp: Math.floor(Date.now() / 1000)
    });
  } catch (error) {
    console.error("Image-to-Image Kling error:", error);
    res.status(500).json({
      error: "Image-to-image generation failed",
      details: error.message
    });
  }
});

const domains = [
  "@AllFreeMail.net",
  "@AllWebEmails.com",
  "@EasyMailer.live",
  "@HorizonsPost.com",
  "@InboxOrigin.com",
  "@MailMagnet.co",
  "@OpenMail.pro",
  "@SolarNyx.com",
];

const TEMP_HEADERS = {
  "accept": "*/*",
  "accept-language": "en-US,en;q=0.9",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0",
  "priority": "u=1, i",
  "referer": "https://temporarymail.com/en/",
  "sec-fetch-dest": "empty",
  "sec-fetch-mode": "cors",
  "sec-fetch-site": "same-origin",
  "sec-gpc": "1"
};

// Route 1: Generate new temporary email
app.get("/temp-email/new", async (req, res) => {
  const genRes = await fetch(
    "https://temporarymail.com/api/?action=generateRandomName&value=1",
    { headers: TEMP_HEADERS }
  );
  const { address: localPart } = await genRes.json();

  const domain = domains[Math.floor(Math.random() * domains.length)];
  const fullAddress = `${localPart}${domain}`;

  const accessRes = await fetch(
    `https://temporarymail.com/api/?action=requestEmailAccess&key=&value=${encodeURIComponent(fullAddress)}&r=${encodeURIComponent("https://duckduckgo.com/")}`,
    { headers: TEMP_HEADERS }
  );
  const accessData = await accessRes.json();

  res.json({
    address: accessData.address,
    secretKey: accessData.secretKey
  });
});

// Route 2: Check inbox using secretKey
app.get("/temp-email/inbox", async (req, res) => {
  const key = req.query.secretKey;
  if (!key) {
    return res.status(400).json({ error: "Missing secretKey parameter" });
  }

  const inboxRes = await fetch(
    `https://temporarymail.com/api/?action=checkInbox&value=${encodeURIComponent(key)}`,
    { headers: TEMP_HEADERS }
  );
  const inboxData = await inboxRes.json();
  res.json(inboxData);
});

// Route 3: View specific email content
app.get("/temp-email/view", async (req, res) => {
  const id = req.query.id;
  if (!id) {
    return res.status(400).json({ error: "Missing `id` query parameter" });
  }

  const emailUrl = `https://temporarymail.com/view/?i=${encodeURIComponent(id)}&width=0`;

  const response = await fetch(emailUrl, {
    headers: {
      "user-agent": "Mozilla/5.0",
      "referer": "https://temporarymail.com/en/",
    },
  });

  if (!response.ok) {
    return res.status(response.status).json({ error: "Failed to fetch email view" });
  }

  const html = await response.text();
  res.set("content-type", "text/html; charset=utf-8");
  res.send(html);
});



// AIEase token management - simple cookie-based approach
let aieaseTokenData = {
  token: null,
  requestCount: 0,
  maxRequests: 3
};

// Function to extract Bearer token from _hjSessionUser_xxxx cookie
function getTokenFromCookies() {
  try {
    // Make a request to get fresh cookies from AIEase
    return fetch("https://www.aiease.ai/text-to-image", {
      method: "GET",
      headers: {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
      }
    }).then(response => {
      const setCookieHeader = response.headers.get('set-cookie') || '';

      // Find cookie that matches _hjSessionUser_xxxx pattern
      const cookies = setCookieHeader.split(',');
      const sessionCookie = cookies.find(cookie =>
        cookie.trim().includes('_hjSessionUser_')
      );

      if (sessionCookie) {
        // Extract the token value from the cookie
        const match = sessionCookie.match(/_hjSessionUser_[^=]+=([^;]+)/);
        if (match && match[1]) {
          const token = decodeURIComponent(match[1]);
          console.log('Token extracted from _hjSessionUser cookie');
          return token;
        }
      }

      console.log('No _hjSessionUser cookie found, using fallback');
      return process.env.AIEASE_BEARER_TOKEN || "";
    });
  } catch (error) {
    console.error('Error getting token from cookies:', error);
    return process.env.AIEASE_BEARER_TOKEN || "";
  }
}

// Function to get current token (refreshes every 3 requests)
async function getAieaseToken() {
  // Check if we need to refresh the token
  if (!aieaseTokenData.token || aieaseTokenData.requestCount >= aieaseTokenData.maxRequests) {
    console.log(`Refreshing token (request count: ${aieaseTokenData.requestCount})`);
    aieaseTokenData.token = await getTokenFromCookies();
    aieaseTokenData.requestCount = 0;
  }

  // Increment request count
  aieaseTokenData.requestCount++;

  return aieaseTokenData.token;
}

app.get("/create-v5", async (req, res) => {
  try {
    const prompt = req.query.prompt;
    const style_id = parseInt(req.query.style_id) || 1;
    const size = req.query.size || "1-1";
    const batchSize = parseInt(req.query.batchSize) || 1;

    if (!prompt) {
      return res.status(400).json({
        error: "Missing required parameter: prompt",
        example: "/create-v5?prompt=a+beautiful+landscape&style_id=1&size=1-1&batchSize=1"
      });
    }

    // Get Bearer token from cookies (refreshes every 3 requests)
    const bearerToken = await getAieaseToken();

    const apiUrl = "https://www.aiease.ai/api/api/gen/v2/text2img";

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
        "Authorization": `Bearer ${bearerToken}`,
        "Content-Type": "application/json",
        "Origin": "https://www.aiease.ai",
        "Referer": "https://www.aiease.ai/text-to-image",
        "Sec-Ch-Ua": "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": "\"Windows\"",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
      },
      body: JSON.stringify({
        gen_type: "art_v1",
        art_v1_extra_data: {
          prompt: prompt,
          style_id: style_id,
          size: size,
          batchSize: batchSize
        }
      })
    });

    if (!response.ok) {
      // Handle authorization errors specifically
      if (response.status === 401) {
        return res.status(500).json({
          error: "Authorization token expired",
          message: "The AIEase API token needs to be refreshed. Please contact the administrator.",
          code: "TOKEN_EXPIRED"
        });
      }

      return res.status(response.status).json({
        error: "AIEase API request failed",
        status: response.status,
        statusText: response.statusText
      });
    }

    const data = await response.json();

    // Handle successful response
    if (data.code === 200 && data.result) {
      // Transform any image URLs to use cdn.picgenv.net domain pattern
      let transformedResult = { ...data.result };

      // If there are image URLs in the response, transform them
      if (data.result.images && Array.isArray(data.result.images)) {
        transformedResult.images = data.result.images.map(imageUrl => {
          // Transform AIEase image URLs to cdn.picgenv.net/aiease/
          if (typeof imageUrl === 'string' && imageUrl.includes('aiease.ai')) {
            return imageUrl.replace(/https:\/\/[^\/]*aiease\.ai/, 'https://cdn.picgenv.net/aiease');
          }
          return imageUrl;
        });
      }

      res.json({
        code: data.code,
        message: data.message || "Success",
        model: "AIEase Art v1",
        prompt: prompt,
        style_id: style_id,
        size: size,
        batchSize: batchSize,
        taskId: data.result.taskId,
        featureUsages: data.result.featureUsages || [],
        result: transformedResult,
        timestamp: Math.floor(Date.now() / 1000)
      });
    } else {
      // Handle API errors
      res.status(500).json({
        error: "AIEase API returned an error",
        code: data.code || "UNKNOWN_ERROR",
        message: data.message || "Unknown error occurred",
        details: data
      });
    }

  } catch (error) {
    console.error("Error in /create-v5 route:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error.message,
      troubleshooting: [
        "Check if the AIEase API is accessible",
        "Verify the Bearer token is valid",
        "Ensure all required parameters are provided",
        "Try with different style_id or size values"
      ]
    });
  }
});

// Token status endpoint for monitoring
app.get("/aiease/token-status", async (req, res) => {
  res.json({
    hasToken: !!aieaseTokenData.token,
    tokenPreview: aieaseTokenData.token ? `${aieaseTokenData.token.substring(0, 20)}...` : "No token",
    requestCount: aieaseTokenData.requestCount,
    maxRequests: aieaseTokenData.maxRequests,
    nextRefreshIn: aieaseTokenData.maxRequests - aieaseTokenData.requestCount,
    timestamp: new Date().toISOString()
  });
});

app.get("/create", async (req, res) => {
  try {
    const prompt = req.query.prompt;
    const negativePrompt = req.query.negativePrompt || "";
    const size = req.query.size;
    const style = req.query.style || "RANDOM";

    if (!prompt || !size) {
      return res.status(400).json({ error: "Missing required parameters: prompt and size" });
    }

    const [width, height] = size.split("x").map(Number);
    if (!width || !height) {
      return res.status(400).json({ error: "Invalid size format. Use 'widthxheight'" });
    }

    const apiUrl = "https://perchanceai.cc/api/model/predict/v4";

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "accept": "application/json, text/plain, */*",
        "content-type": "application/json",
        "origin": "https://perchanceai.cc",
        "referer": "https://perchanceai.cc/",
        "user-agent": "Mozilla/5.0",
      },
      body: JSON.stringify({
        prompt,
        negative_prompt: negativePrompt,
        width,
        height,
        key: style, // Using style parameter but API expects 'key'
      }),
    });

    const data = await response.json();

    // Transform the response to the desired format
    if (data && data.code === 0 && data.data && data.data.length > 0) {
      const imageData = data.data[0];

      // Transform the image URL from r.perchanceai.cc/static/ to cdn.picgenv.net/fluxai3/
      let transformedUrl = imageData.url || "";
      transformedUrl = transformedUrl.replace("https://r.perchanceai.cc/static/", "https://cdn.picgenv.net/fluxai3/");

      res.json({
        status: imageData.status || "DONE",
        style: imageData.style || "",
        safeState: imageData.safeState || "UNKNOWN",
        negativePrompt: imageData.negativePrompt || "",
        url: transformedUrl
      });
    } else {
      // If the response doesn't have the expected structure, return it as is
      res.json(data);
    }
  } catch (error) {
    console.error("Error in /create route:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Apply RapidAPI validation middleware to all routes
app.use(validateRapidAPIRequest);

// Start the server
app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});