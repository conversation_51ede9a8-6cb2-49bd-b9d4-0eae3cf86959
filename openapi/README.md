# FluxAPI OpenAPI Specification

This folder contains the OpenAPI 3.0.3 specification for FluxAPI, providing a complete, machine-readable description of the API.

**🔧 Recent Updates:**
- ✅ Removed authentication requirements - now publicly accessible
- ✅ Fixed streaming issues for custom models (now non-streaming only)
- ✅ Updated OpenAPI specification with proper examples and validation
- ✅ Improved error handling and response formats

## 📁 Files

- **`fluxapi.yaml`** - Complete OpenAPI 3.0.3 specification ✅ **VALIDATED**
- **`swagger-ui.html`** - Interactive Swagger UI documentation viewer
- **`README.md`** - This documentation file
- **`examples/`** - Request/response examples and use cases
  - `chat-examples.json` - Chat and AI endpoint examples
  - `image-examples.json` - Image generation examples
  - `utility-examples.json` - Utility service examples
- **`FluxAPI.postman_collection.json`** - Postman collection for testing
- **`FluxAPI.insomnia.json`** - Insomnia workspace (partial)

## 🚀 Usage

### View in Swagger UI

1. **Local HTML Viewer** (Recommended):
   ```bash
   # Open the included Swagger UI
   open openapi/swagger-ui.html
   # or
   python -m http.server 8000
   # Then visit http://localhost:8000/openapi/swagger-ui.html
   ```

2. **Online Swagger Editor**:
   ```
   https://editor.swagger.io/
   ```
   Copy and paste the content of `fluxapi.yaml`

3. **Local Swagger UI with Docker**:
   ```bash
   docker run -p 8080:8080 -e SWAGGER_JSON=/app/fluxapi.yaml -v $(pwd):/app swaggerapi/swagger-ui
   # Then open http://localhost:8080
   ```

4. **VS Code Extension**:
   Install "Swagger Viewer" extension and open `fluxapi.yaml`

### Generate Client SDKs

Use OpenAPI Generator to create client libraries:

```bash
# Install OpenAPI Generator
npm install @openapitools/openapi-generator-cli -g

# Generate JavaScript client
openapi-generator-cli generate -i fluxapi.yaml -g javascript -o ./clients/javascript

# Generate Python client
openapi-generator-cli generate -i fluxapi.yaml -g python -o ./clients/python

# Generate TypeScript client
openapi-generator-cli generate -i fluxapi.yaml -g typescript-axios -o ./clients/typescript
```

### Validate Specification

```bash
# Using Swagger CLI
npm install -g @apidevtools/swagger-cli
swagger-cli validate fluxapi.yaml

# Using OpenAPI Generator
openapi-generator-cli validate -i fluxapi.yaml
```

## 📋 API Overview

### Base Information
- **Version**: 1.0.0
- **Base URL**: `http://localhost:3000`
- **Authentication**: None required - publicly accessible
- **Content Type**: `application/json`

### Endpoint Categories

#### 🏥 Health & System
- `GET /ping` - Health check
- `GET /health` - System status
- `GET /v1/models` - Available AI models

#### 💬 Chat & AI
- `POST /v1/chat/completions` - OpenAI-compatible chat (Together AI) - **Supports Streaming**
- `GET /custom/{model}` - Custom model chat (Flowith API) - **Non-streaming**
- `POST /v1/chat/completions/custom` - OpenAI-compatible custom models - **Non-streaming**
- `GET /translate` - Text translation

#### 🎨 Image Generation
- `GET /create-v1` - Flux model generation
- `GET /create-v2` - Aspect ratio controlled
- `GET /create-v3` - Pollinations AI
- `GET /create-v4` - Style presets
- `GET /img2img` - Image transformation

#### 🛠️ Utilities
- `GET /check` - Username availability (40+ platforms)
- `GET /Geo-detect` - IP geolocation & device detection
- `GET /tts` - Text-to-speech conversion

## 🔧 Key Features

### OpenAI Compatibility
The API follows OpenAI's format for:
- Request/response structures
- Error handling
- Streaming responses (Together AI models only)
- Model naming conventions

### Streaming Support
- **Together AI Models** (`/v1/chat/completions`): ✅ Full streaming support
- **Custom Models** (`/v1/chat/completions/custom`): ❌ Non-streaming only
- **Flowith API Models**: Non-streaming responses for better reliability

### Streaming Support
Multiple endpoints support real-time streaming:
- Chat completions with Server-Sent Events
- Custom model responses
- Proper streaming format compliance

### Comprehensive Error Handling
- Standardized error responses
- HTTP status codes
- Detailed error messages
- Troubleshooting guidance

### Model Support
- **Together AI Models**: 5 high-quality models
- **Custom Models**: 4 specialized models via Flowith
- **Image Models**: Multiple generation engines

## 📖 Examples

### Simple Chat Request (Together AI - Streaming Supported)
```bash
curl -X POST "http://localhost:3000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "Llama-3.3-70B-Instruct-Turbo",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ],
    "stream": true
  }'
```

### Custom Model Chat (Flowith API - Non-streaming)
```bash
curl -X POST "http://localhost:3000/v1/chat/completions/custom" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "grok-3-mini",
    "messages": [
      {"role": "user", "content": "What is 2+2?"}
    ]
  }'
```

### Image Generation
```bash
curl "http://localhost:3000/create-v4?prompt=sunset&size=1024x1024&style=anime"
```

### Username Check
```bash
curl "http://localhost:3000/check?username=johndoe"
```

## 🔒 Security

### Authentication
No authentication required - all endpoints are publicly accessible:
```yaml
# No security schemes defined - open API
```

### Rate Limiting
Recommended limits per endpoint category:
- Chat API: 10 requests/minute
- Image Generation: 5 requests/minute
- Utilities: 30 requests/minute

## 🧪 Testing

### Postman Collection
Import the OpenAPI spec into Postman:
1. Open Postman
2. Import → Link → Paste OpenAPI spec URL
3. Generate collection automatically

### Insomnia
1. Create new request collection
2. Import from OpenAPI spec
3. Configure environment variables

### cURL Examples
See the main documentation for comprehensive cURL examples.

## 📚 Integration

### Client Libraries
The OpenAPI spec enables automatic generation of client libraries in 50+ languages:

- **JavaScript/TypeScript**: axios, fetch, node
- **Python**: requests, aiohttp, urllib3
- **Java**: okhttp, retrofit, apache
- **Go**: net/http, resty
- **PHP**: guzzle, curl
- **Ruby**: faraday, net/http
- **C#**: HttpClient, RestSharp

### Framework Integration
- **Next.js**: Use with SWR or React Query
- **Vue.js**: Use with Axios or Fetch API
- **Angular**: Use with HttpClient
- **React**: Use with Axios or Fetch API
- **Express.js**: Use as middleware or client

## 🔄 Updates

The OpenAPI specification is updated with each API version:
- New endpoints are added
- Schema changes are documented
- Breaking changes are clearly marked
- Backward compatibility is maintained where possible

## 📞 Support

For issues with the OpenAPI specification:
1. Check the validation status
2. Review the examples
3. Consult the main API documentation
4. Create an issue in the repository

---

**OpenAPI Specification Version**: 3.0.3  
**API Version**: 1.0.0  
**Last Updated**: 2025-07-12
