{"_type": "export", "__export_format": 4, "__export_date": "2025-07-12T10:30:00.000Z", "__export_source": "insomnia.desktop.app:v2023.5.8", "resources": [{"_id": "req_group_health", "_type": "request_group", "parentId": "wrk_fluxapi", "modified": 1752297000000, "created": 1752297000000, "name": "Health & System", "description": "Health checks and system information endpoints", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1752297000000}, {"_id": "req_ping", "_type": "request", "parentId": "req_group_health", "modified": 1752297000000, "created": 1752297000000, "url": "{{ _.baseUrl }}/ping", "name": "<PERSON>", "description": "Simple health check endpoint", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1752297000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_health", "_type": "request", "parentId": "req_group_health", "modified": 1752297000000, "created": 1752297000000, "url": "{{ _.baseUrl }}/health", "name": "Health Check", "description": "Detailed system health status", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1752296000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_models", "_type": "request", "parentId": "req_group_health", "modified": 1752297000000, "created": 1752297000000, "url": "{{ _.baseUrl }}/v1/models", "name": "List Models", "description": "Get available AI models", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1752295000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_group_chat", "_type": "request_group", "parentId": "wrk_fluxapi", "modified": 1752297000000, "created": 1752297000000, "name": "Chat & AI", "description": "AI chat completions and language services", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1752296000000}, {"_id": "req_chat_completions", "_type": "request", "parentId": "req_group_chat", "modified": 1752297000000, "created": 1752297000000, "url": "{{ _.baseUrl }}/v1/chat/completions", "name": "Chat Completions", "description": "OpenAI-compatible chat completions", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"model\": \"Llama-3.3-70B-Instruct-Turbo\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a helpful assistant.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Hello! How are you?\"\n    }\n  ],\n  \"max_tokens\": 150,\n  \"temperature\": 0.7\n}"}, "parameters": [], "headers": [{"id": "pair_content_type", "name": "Content-Type", "value": "application/json"}, {"id": "pair_auth", "name": "X-RapidAPI-Proxy-Secret", "value": "{{ _.rapidApiSecret }}"}], "authentication": {}, "metaSortKey": -1752297000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_custom_get", "_type": "request", "parentId": "req_group_chat", "modified": 1752297000000, "created": 1752297000000, "url": "{{ _.baseUrl }}/custom/grok-3-mini", "name": "Custom Model Chat (GET)", "description": "Chat with custom models via GET", "method": "GET", "body": {}, "parameters": [{"id": "pair_prompt", "name": "prompt", "value": "Hello, how are you?"}, {"id": "pair_system", "name": "system", "value": "You are a helpful assistant."}, {"id": "pair_stream", "name": "stream", "value": "false"}], "headers": [{"id": "pair_auth", "name": "X-RapidAPI-Proxy-Secret", "value": "{{ _.rapidApiSecret }}"}], "authentication": {}, "metaSortKey": -1752296000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_custom_post", "_type": "request", "parentId": "req_group_chat", "modified": 1752297000000, "created": 1752297000000, "url": "{{ _.baseUrl }}/v1/chat/completions/custom", "name": "Custom Model Chat (POST)", "description": "OpenAI-compatible custom models", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"model\": \"grok-3-mini\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a helpful assistant.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"What is 2+2?\"\n    }\n  ],\n  \"stream\": false\n}"}, "parameters": [], "headers": [{"id": "pair_content_type", "name": "Content-Type", "value": "application/json"}, {"id": "pair_auth", "name": "X-RapidAPI-Proxy-Secret", "value": "{{ _.rapidApiSecret }}"}], "authentication": {}, "metaSortKey": -1752295000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_group_images", "_type": "request_group", "parentId": "wrk_fluxapi", "modified": 1752297000000, "created": 1752297000000, "name": "Image Generation", "description": "AI image generation and transformation", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1752295000000}, {"_id": "req_create_v4", "_type": "request", "parentId": "req_group_images", "modified": 1752297000000, "created": 1752297000000, "url": "{{ _.baseUrl }}/create-v4", "name": "Create V4 (Style Presets)", "description": "Generate images with style presets", "method": "GET", "body": {}, "parameters": [{"id": "pair_prompt", "name": "prompt", "value": "a robot in a garden"}, {"id": "pair_size", "name": "size", "value": "1024x1024"}, {"id": "pair_style", "name": "style", "value": "cyberpunk"}], "headers": [{"id": "pair_auth", "name": "X-RapidAPI-Proxy-Secret", "value": "{{ _.rapidApiSecret }}"}], "authentication": {}, "metaSortKey": -1752297000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_group_utilities", "_type": "request_group", "parentId": "wrk_fluxapi", "modified": 1752297000000, "created": 1752297000000, "name": "Utilities", "description": "Username checking, geolocation, and other utilities", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1752294000000}, {"_id": "req_username_check", "_type": "request", "parentId": "req_group_utilities", "modified": 1752297000000, "created": 1752297000000, "url": "{{ _.baseUrl }}/check", "name": "Check Username", "description": "Check username availability across platforms", "method": "GET", "body": {}, "parameters": [{"id": "pair_username", "name": "username", "value": "johndoe"}], "headers": [{"id": "pair_auth", "name": "X-RapidAPI-Proxy-Secret", "value": "{{ _.rapidApiSecret }}"}], "authentication": {}, "metaSortKey": -1752297000000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "env_base", "_type": "environment", "parentId": "wrk_fluxapi", "modified": 1752297000000, "created": 1752297000000, "name": "Base Environment", "data": {"baseUrl": "http://localhost:3000", "rapidApiSecret": "your-secret-key"}, "dataPropertyOrder": {"&": ["baseUrl", "rapidApiSecret"]}, "color": null, "isPrivate": false, "metaSortKey": 1752297000000}, {"_id": "jar_cookies", "_type": "cookie_jar", "parentId": "wrk_fluxapi", "modified": 1752297000000, "created": 1752297000000, "name": "<PERSON><PERSON><PERSON>", "cookies": []}, {"_id": "spc_fluxapi", "_type": "api_spec", "parentId": "wrk_fluxapi", "modified": 1752297000000, "created": 1752297000000, "fileName": "FluxAPI", "contents": "", "contentType": "yaml", "source": "file"}, {"_id": "wrk_fluxapi", "_type": "workspace", "parentId": null, "modified": 1752297000000, "created": 1752297000000, "name": "FluxAPI", "description": "Complete API workspace for FluxAPI - AI chat, image generation, and utilities", "scope": "collection"}]}