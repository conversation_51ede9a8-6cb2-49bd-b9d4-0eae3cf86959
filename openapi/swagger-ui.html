<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FluxAPI - OpenAPI Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin:0;
            background: #fafafa;
        }
        .swagger-ui .topbar {
            background-color: #2c3e50;
        }
        .swagger-ui .topbar .download-url-wrapper .select-label {
            color: #fff;
        }
        .swagger-ui .topbar .download-url-wrapper input[type=text] {
            border: 2px solid #3498db;
        }
        .swagger-ui .topbar .download-url-wrapper .download-url-button {
            background: #3498db;
            color: #fff;
            border: 2px solid #3498db;
        }
        .custom-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 0;
        }
        .custom-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .custom-header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .quick-links {
            background: #34495e;
            color: white;
            padding: 15px;
            text-align: center;
        }
        .quick-links a {
            color: #3498db;
            text-decoration: none;
            margin: 0 15px;
            font-weight: 500;
        }
        .quick-links a:hover {
            color: #5dade2;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="custom-header">
        <h1>🚀 FluxAPI</h1>
        <p>Complete OpenAPI 3.0.3 Specification</p>
    </div>
    
    <div class="quick-links">
        <a href="#tag/Health-&-System">Health & System</a>
        <a href="#tag/Chat-&-AI">Chat & AI</a>
        <a href="#tag/Image-Generation">Image Generation</a>
        <a href="#tag/Utilities">Utilities</a>
        <span style="margin: 0 20px;">|</span>
        <a href="./README.md">Documentation</a>
        <a href="./examples/">Examples</a>
        <a href="./FluxAPI.postman_collection.json">Postman Collection</a>
    </div>

    <div id="swagger-ui"></div>

    <script src="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            // Begin Swagger UI call region
            const ui = SwaggerUIBundle({
                url: './fluxapi.yaml',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                docExpansion: "list",
                filter: true,
                showExtensions: true,
                showCommonExtensions: true,
                tryItOutEnabled: true,
                requestInterceptor: function(request) {
                    // No authentication headers needed
                    return request;
                },
                onComplete: function() {
                    console.log('FluxAPI OpenAPI documentation loaded successfully!');
                    
                    // Add custom styling
                    const style = document.createElement('style');
                    style.textContent = `
                        .swagger-ui .info .title {
                            color: #2c3e50;
                        }
                        .swagger-ui .scheme-container {
                            background: #ecf0f1;
                            border: 1px solid #bdc3c7;
                        }
                        .swagger-ui .opblock.opblock-get {
                            border-color: #27ae60;
                            background: rgba(39, 174, 96, 0.1);
                        }
                        .swagger-ui .opblock.opblock-post {
                            border-color: #3498db;
                            background: rgba(52, 152, 219, 0.1);
                        }
                        .swagger-ui .opblock.opblock-get .opblock-summary-method {
                            background: #27ae60;
                        }
                        .swagger-ui .opblock.opblock-post .opblock-summary-method {
                            background: #3498db;
                        }
                    `;
                    document.head.appendChild(style);
                }
            });
            // End Swagger UI call region

            window.ui = ui;
        };
    </script>
</body>
</html>
