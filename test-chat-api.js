#!/usr/bin/env node

// Test script for the new POST /v1/chat/completions endpoint
const fetch = require('node-fetch');

const API_BASE = 'http://localhost:3000';

// Test data
const testCases = [
  {
    name: "Simple Chat",
    data: {
      model: "Llama-3.3-70B-Instruct-Turbo",
      messages: [
        {
          role: "system",
          content: "You are a helpful assistant."
        },
        {
          role: "user",
          content: "Hello! How are you?"
        }
      ],
      max_tokens: 150,
      temperature: 0.7,
      stream: false
    }
  },
  {
    name: "Conversation with History",
    data: {
      model: "Llama-3.3-70B-Instruct-Turbo",
      messages: [
        {
          role: "system",
          content: "You are a helpful assistant."
        },
        {
          role: "user",
          content: "What's the capital of France?"
        },
        {
          role: "assistant",
          content: "The capital of France is Paris."
        },
        {
          role: "user",
          content: "What's the population of that city?"
        }
      ],
      max_tokens: 200,
      temperature: 0.5,
      stream: false
    }
  },
  {
    name: "Streaming Chat",
    data: {
      model: "Llama-3.3-70B-Instruct-Turbo",
      messages: [
        {
          role: "system",
          content: "You are a creative storyteller."
        },
        {
          role: "user",
          content: "Tell me a short story about a robot learning to paint."
        }
      ],
      max_tokens: 300,
      temperature: 0.8,
      stream: true
    }
  }
];

async function testNonStreaming(testCase) {
  console.log(`\n🧪 Testing: ${testCase.name}`);
  console.log('📤 Request:', JSON.stringify(testCase.data, null, 2));
  
  try {
    const response = await fetch(`${API_BASE}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testCase.data)
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Success!');
      console.log('📥 Response:', JSON.stringify(data, null, 2));
    } else {
      console.log('❌ Error!');
      console.log('📥 Error Response:', JSON.stringify(data, null, 2));
    }
  } catch (error) {
    console.log('💥 Request failed:', error.message);
  }
}

async function testStreaming(testCase) {
  console.log(`\n🌊 Testing Streaming: ${testCase.name}`);
  console.log('📤 Request:', JSON.stringify(testCase.data, null, 2));
  
  try {
    const response = await fetch(`${API_BASE}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testCase.data)
    });

    if (response.ok) {
      console.log('✅ Stream started!');
      console.log('📥 Streaming response:');
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data.trim() && data !== '[DONE]') {
              try {
                const parsed = JSON.parse(data);
                if (parsed.type === 'content') {
                  process.stdout.write(parsed.content);
                  fullContent += parsed.content;
                } else if (parsed.type === 'done') {
                  console.log('\n🏁 Stream completed');
                } else if (parsed.type === 'error') {
                  console.log('\n❌ Stream error:', parsed.error);
                }
              } catch (e) {
                // Skip invalid JSON
              }
            }
          }
        }
      }
      
      console.log('\n📝 Full content:', fullContent);
    } else {
      const data = await response.json();
      console.log('❌ Error!');
      console.log('📥 Error Response:', JSON.stringify(data, null, 2));
    }
  } catch (error) {
    console.log('💥 Request failed:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting FluxAPI Chat Tests...');
  
  // Test non-streaming cases
  for (const testCase of testCases.filter(t => !t.data.stream)) {
    await testNonStreaming(testCase);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
  }
  
  // Test streaming cases
  for (const testCase of testCases.filter(t => t.data.stream)) {
    await testStreaming(testCase);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
  }
  
  console.log('\n✨ All tests completed!');
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(`${API_BASE}/ping`);
    if (response.ok) {
      console.log('✅ Server is running');
      return true;
    }
  } catch (error) {
    console.log('❌ Server is not running. Please start the server first with: npm start');
    return false;
  }
}

// Main execution
async function main() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await runTests();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testNonStreaming, testStreaming, runTests };
