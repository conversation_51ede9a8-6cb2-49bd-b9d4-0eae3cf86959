# AIEase API Token Configuration

## Overview

The `/create-v5` endpoint now includes automatic Bearer token refresh functionality that refreshes the token after every 3 requests to ensure continuous API access.

## How It Works

### Automatic Token Refresh
- **Trigger**: Token is automatically refreshed after every 3 requests
- **Fallback**: If a 401 error occurs, the system immediately attempts to refresh the token and retry the request
- **Graceful Degradation**: If token refresh fails, a placeholder token is used as fallback

### Token Management Variables
```javascript
let aieaseTokenData = {
  token: null,           // Current Bearer token
  requestCount: 0,       // Number of requests made with current token
  maxRequests: 3         // Maximum requests before refresh (configurable)
};
```

## Configuration

### Environment Variables
Add these to your `.env` file:

```env
# AIEase API Credentials (required for automatic token refresh)
AIEASE_EMAIL=<EMAIL>
AIEASE_PASSWORD=your_password
```

### Customizing Refresh Frequency
To change the refresh frequency, modify the `maxRequests` value in the `aieaseTokenData` object:

```javascript
let aieaseTokenData = {
  token: null,
  requestCount: 0,
  maxRequests: 5  // Change this to refresh every 5 requests instead of 3
};
```

## API Endpoints

### Main Image Generation Endpoint
```
GET /create-v5?prompt=your_prompt&style_id=1&size=1-1&batchSize=1
```

### Administrative Endpoints

#### Manual Token Refresh
```
GET /aiease/refresh-token
```
Forces an immediate token refresh. Useful for testing or manual maintenance.

**Response:**
```json
{
  "success": true,
  "message": "AIEase token refreshed successfully",
  "tokenPreview": "eyJhbGciOiJIUzI1NiIs...",
  "requestCount": 1,
  "maxRequests": 3,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

#### Token Status Check
```
GET /aiease/token-status
```
Returns current token status and usage information.

**Response:**
```json
{
  "hasToken": true,
  "tokenPreview": "eyJhbGciOiJIUzI1NiIs...",
  "requestCount": 2,
  "maxRequests": 3,
  "nextRefreshIn": 1,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Implementation Details

### Token Refresh Function
The `refreshAieaseToken()` function:
1. Makes a POST request to the AIEase login endpoint
2. Extracts the Bearer token from the response
3. Updates the token data and resets the request counter
4. Falls back to a placeholder token if refresh fails

### Auto-Refresh Logic
The `getAieaseToken()` function:
1. Checks if token needs refresh (null token or max requests reached)
2. Calls refresh function if needed
3. Increments request counter
4. Returns current valid token

### Error Handling
- **401 Unauthorized**: Automatically triggers token refresh and retries the request once
- **Refresh Failure**: Falls back to placeholder token with appropriate error logging
- **Network Errors**: Graceful error handling with detailed error messages

## TODO: Complete Implementation

Currently, the token refresh uses a placeholder login endpoint. To complete the implementation:

1. **Find the actual AIEase login/auth endpoint**
2. **Update the login request in `refreshAieaseToken()`**
3. **Test with real credentials**
4. **Adjust token extraction logic based on actual API response format**

### Example of what needs to be updated:
```javascript
// Replace this placeholder URL with the actual AIEase auth endpoint
const loginResponse = await fetch("https://www.aiease.ai/api/auth/login", {
  // ... existing headers ...
  body: JSON.stringify({
    email: process.env.AIEASE_EMAIL,
    password: process.env.AIEASE_PASSWORD
  })
});
```

## Testing

### Test Token Refresh
1. Make 3 requests to `/create-v5` to trigger automatic refresh
2. Check `/aiease/token-status` to see request count
3. Use `/aiease/refresh-token` to manually refresh
4. Monitor console logs for refresh activity

### Monitor Token Usage
```bash
# Check current token status
curl http://localhost:3000/aiease/token-status

# Force token refresh
curl http://localhost:3000/aiease/refresh-token
```

## Security Notes

- Store credentials securely in environment variables
- Never commit actual credentials to version control
- Consider implementing token encryption for additional security
- Monitor token refresh logs for suspicious activity
