# AIEase Cookie-Based Token Management

## Overview

The `/create-v5` endpoint automatically extracts Bearer tokens from `_hjSessionUser_xxxx` cookies and refreshes them every 3 requests.

## How It Works

### Cookie-Based Token Extraction
- **Primary Source**: Extracts Bearer token from `userStore` cookie (contains <PERSON><PERSON><PERSON> with user.token)
- **Fallback Source**: Extracts from `_hjSessionUser_xxxx` cookies if userStore not available
- **Refresh Trigger**: Token refreshes automatically after every 3 requests
- **Important**: The `userStore` cookie is only available after logging into AIEase website
- **Fallback**: Uses `AIEASE_BEARER_TOKEN` environment variable if cookie extraction fails

### Implementation Details

```javascript
// Token management object
let aieaseTokenData = {
  token: null,           // Current Bearer token from cookies
  requestCount: 0,       // Number of requests made
  maxRequests: 3         // Refresh every 3 requests
};

// Cookie extraction function
function getTokenFromCookies() {
  // Makes GET request to https://www.aiease.ai/text-to-image
  // Extracts _hjSessionUser_xxxx cookie from Set-Cookie header
  // Returns decoded token value
}

// Auto-refresh function
async function getAieaseToken() {
  // Checks if refresh needed (no token OR requestCount >= 3)
  // Calls getTokenFromCookies() if refresh needed
  // Increments requestCount
  // Returns current token
}
```

## API Endpoints

### Main Image Generation
```
GET /create-v5?prompt=your_prompt&style_id=1&size=1-1&batchSize=1
```

### Token Status Monitoring
```
GET /aiease/token-status
```

**Response:**
```json
{
  "hasToken": true,
  "tokenPreview": "eyJhbGciOiJIUzI1NiIs...",
  "requestCount": 2,
  "maxRequests": 3,
  "nextRefreshIn": 1,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Getting the userStore Cookie

### Step-by-Step Instructions

1. **Open Browser**: Go to `https://www.aiease.ai/app/generate-images`
2. **Login/Register**: You need to be logged in to get the `userStore` cookie
3. **Open Dev Tools**: Press F12 or right-click → Inspect
4. **Go to Application Tab**: Click on "Application" or "Storage" tab
5. **Find Cookies**: Click on "Cookies" → "https://www.aiease.ai"
6. **Look for userStore**: Find the cookie named `userStore`
7. **Copy the Value**: The value contains JSON with the token

### userStore Cookie Structure
```json
{
  "user": {
    "username": "aiease_xxxxx",
    "email": "<EMAIL>",
    "id": "12345",
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "created_at": 1752535837952
  },
  "feature_free_count_object": {...},
  "creditsInfo": {...}
}
```

## Configuration

### Environment Variable (Optional Fallback)
```env
# Fallback token if cookie extraction fails
AIEASE_BEARER_TOKEN=your_fallback_token_here
```

### Customizing Refresh Frequency
Change `maxRequests` in the code:
```javascript
let aieaseTokenData = {
  token: null,
  requestCount: 0,
  maxRequests: 5  // Refresh every 5 requests instead of 3
};
```

## Usage Examples

### Basic Image Generation
```bash
curl "http://localhost:3000/create-v5?prompt=beautiful+landscape"
```

### With All Parameters
```bash
curl "http://localhost:3000/create-v5?prompt=cyberpunk+city&style_id=2&size=2-3&batchSize=2"
```

### Check Token Status
```bash
curl "http://localhost:3000/aiease/token-status"
```

## How Token Refresh Works

1. **First Request**: No token exists, fetches fresh cookies from AIEase
2. **Requests 1-3**: Uses the same token, increments counter
3. **Request 4**: Counter reaches 3, automatically refreshes token from cookies
4. **Continues**: Repeats the cycle every 3 requests

## Cookie Pattern Details

The system looks for cookies with this pattern:
- **Name**: `_hjSessionUser_xxxx` (where xxxx can be any characters)
- **Location**: Set-Cookie header from `https://www.aiease.ai/text-to-image`
- **Extraction**: Uses regex `/_hjSessionUser_[^=]+=([^;]+)/` to extract value
- **Decoding**: URL decodes the cookie value before using as Bearer token

## Error Handling

- **Cookie Not Found**: Falls back to `AIEASE_BEARER_TOKEN` environment variable
- **Network Error**: Uses fallback token or placeholder
- **Invalid Token**: Logs error and continues with available token

## Monitoring

Check the console logs for:
- `"Refreshing token (request count: X)"` - When refresh happens
- `"Token extracted from _hjSessionUser cookie"` - Successful extraction
- `"No _hjSessionUser cookie found, using fallback"` - When fallback is used

## Testing

1. **Monitor Token Usage**:
   ```bash
   curl http://localhost:3000/aiease/token-status
   ```

2. **Test Auto-Refresh**:
   - Make 3 requests to `/create-v5`
   - Check token status after each request
   - Observe request count and token refresh

3. **Check Console Logs**:
   - Watch for refresh messages
   - Monitor cookie extraction success/failure
