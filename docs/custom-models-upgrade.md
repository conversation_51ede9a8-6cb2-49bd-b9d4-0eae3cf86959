# Custom Models Endpoint Upgrade

## 🚀 Overview

The `/custom/:model` endpoint has been completely upgraded to use the new Flowith API provider with enhanced functionality, better model support, and improved reliability.

## ✨ What's New

### 🔄 **API Provider Change**
- **Old**: Generic external provider
- **New**: Flowith API (`https://edge.flowith.net/ai/chat?mode=general`)
- **Benefits**: More reliable, better streaming, enhanced model support

### 🤖 **Supported Models**
- `grok-3-mini` - Grok's efficient mini model
- `gemini-2.5-flash` - Google's fast Gemini model  
- `gpt-4.1-nano` - OpenAI's nano variant
- `gpt-4.1-mini` - OpenAI's mini variant

### 🆕 **New Endpoints**

#### 1. Enhanced GET Endpoint (Backward Compatible)
```bash
GET /custom/:model?prompt=...&system=...&stream=true/false
```

#### 2. New OpenAI-Compatible POST Endpoint
```bash
POST /v1/chat/completions/custom
Content-Type: application/json
```

## 📋 **Technical Implementation**

### **HTTP Headers (Exact Match)**
The implementation uses the exact headers from the working curl example:
```javascript
{
  "accept": "*/*",
  "accept-language": "en-US,en;q=0.9",
  "authorization": "",
  "content-type": "application/json",
  "origin": "https://flowith.net",
  "priority": "u=1, i",
  "referer": "https://flowith.net/",
  "responsetype": "stream",
  "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
  "sec-ch-ua-mobile": "?0",
  "sec-ch-ua-platform": "\"Windows\"",
  "sec-fetch-dest": "empty",
  "sec-fetch-mode": "cors",
  "sec-fetch-site": "same-site",
  "sec-gpc": "1",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}
```

### **NodeId Implementation**
- **Primary**: Attempts to get nodeId from OPTIONS request
- **Fallback**: Generates UUID v4 if OPTIONS fails
- **Usage**: Required field in Flowith API request body

### **Request Format**
```json
{
  "model": "grok-3-mini",
  "messages": [
    {"role": "system", "content": "You are helpful"},
    {"role": "user", "content": "Hello"}
  ],
  "stream": true,
  "nodeId": "generated-uuid"
}
```

## 🔧 **Usage Examples**

### **GET Endpoint (Backward Compatible)**
```bash
# Simple chat
curl "http://localhost:3000/custom/grok-3-mini?prompt=Hello&stream=false"

# With system message
curl "http://localhost:3000/custom/gemini-2.5-flash?prompt=Hello&system=You%20are%20helpful&stream=false"

# Streaming
curl "http://localhost:3000/custom/gpt-4.1-mini?prompt=Tell%20a%20joke&stream=true"
```

### **POST Endpoint (OpenAI Compatible)**
```bash
# Simple chat
curl -X POST "http://localhost:3000/v1/chat/completions/custom" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "grok-3-mini",
    "messages": [
      {"role": "user", "content": "What is 2+2?"}
    ],
    "stream": false
  }'

# Conversation with history
curl -X POST "http://localhost:3000/v1/chat/completions/custom" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {"role": "system", "content": "You are helpful"},
      {"role": "user", "content": "Hello"},
      {"role": "assistant", "content": "Hi there!"},
      {"role": "user", "content": "How are you?"}
    ],
    "stream": false
  }'

# Streaming
curl -X POST "http://localhost:3000/v1/chat/completions/custom" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4.1-nano",
    "messages": [
      {"role": "user", "content": "Tell me a story"}
    ],
    "stream": true
  }'
```

## 📊 **Response Formats**

### **Non-Streaming Response**
```json
{
  "id": "custom-1234567890",
  "object": "chat.completion",
  "created": 1234567890,
  "model": "grok-3-mini",
  "choices": [{
    "index": 0,
    "message": {
      "role": "assistant",
      "content": "Hello! How can I help you?"
    },
    "finish_reason": "stop"
  }],
  "usage": {
    "prompt_tokens": 0,
    "completion_tokens": 0,
    "total_tokens": 0
  },
  "nodeId": "uuid-generated"
}
```

### **Streaming Response (GET)**
```
data: {"type":"metadata","model":"grok-3-mini","nodeId":"uuid","timestamp":"2025-07-12T..."}

data: {"type":"content","content":"Hello","timestamp":"2025-07-12T..."}

data: {"type":"done","full_content":"Hello! How can I help?","timestamp":"2025-07-12T..."}
```

### **Streaming Response (POST - OpenAI Format)**
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1234567890,"model":"grok-3-mini","choices":[{"index":0,"delta":{"content":"Hello"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1234567890,"model":"grok-3-mini","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]
```

## ✅ **Error Handling**

### **Model Validation**
```json
{
  "error": "Model 'unsupported-model' is not supported",
  "supported_models": ["grok-3-mini", "gemini-2.5-flash", "gpt-4.1-nano", "gpt-4.1-mini"],
  "example": "/custom/grok-3-mini?prompt=Hello+world&system=You+are+helpful+assistant"
}
```

### **API Errors**
```json
{
  "error": {
    "message": "Failed to process request with Flowith API",
    "type": "api_error",
    "code": "flowith_error",
    "details": "Specific error message"
  },
  "troubleshooting": [
    "Try a different supported model",
    "Simplify your prompt",
    "Check Flowith API availability"
  ],
  "supported_models": ["grok-3-mini", "gemini-2.5-flash", "gpt-4.1-nano", "gpt-4.1-mini"],
  "api_provider": "Flowith (edge.flowith.net)"
}
```

## 🧪 **Testing**

Run the comprehensive test suite:
```bash
node test-custom-models.js
```

**Test Coverage:**
- ✅ All 4 supported models
- ✅ GET and POST endpoints
- ✅ Streaming and non-streaming
- ✅ Error handling
- ✅ Model validation
- ✅ Conversation history
- ✅ System messages

## 🔄 **Migration Guide**

### **For Existing Users**
- **No changes required** - GET endpoint remains backward compatible
- **Same URL structure**: `/custom/:model`
- **Same parameters**: `prompt`, `system`, `stream`

### **For New Integrations**
- **Recommended**: Use POST `/v1/chat/completions/custom` for OpenAI compatibility
- **Benefits**: Better error handling, standard format, conversation history support

## 🎯 **Key Benefits**

1. **✅ Backward Compatibility**: Existing integrations continue to work
2. **✅ OpenAI Compatibility**: New POST endpoint matches OpenAI format
3. **✅ Better Models**: 4 high-quality models from different providers
4. **✅ Improved Streaming**: More reliable real-time responses
5. **✅ Enhanced Error Handling**: Clear error messages and troubleshooting
6. **✅ Robust Implementation**: Proper headers, nodeId handling, fallbacks
7. **✅ Comprehensive Testing**: Full test suite included

## 🔗 **Related Documentation**

- [Chat & AI Endpoints](./chat-ai.md) - Updated with new endpoints
- [Examples & Integration](./examples.md) - Code examples for both endpoints
- [API Reference](./api-reference.md) - Complete parameter reference

---

**Upgrade completed successfully! 🎉**
