# FluxAPI Documentation

Complete API documentation for all available endpoints.

## 📚 Documentation Structure

- [Chat & AI Endpoints](./chat-ai.md) - OpenAI-compatible chat completions and AI services
- [Image Generation](./image-generation.md) - Multiple AI image generation endpoints
- [Utility Services](./utilities.md) - Username checking, geolocation, email services
- [Health & System](./health-system.md) - Health checks and system information
- [Examples](./examples.md) - Code examples and integration guides

## 🚀 Quick Start

### Base URL
```
http://localhost:3000
```

### Authentication
Most endpoints require RapidAPI proxy secret validation. Set the header:
```
X-RapidAPI-Proxy-Secret: your-secret-key
```

### Content Type
For POST requests, use:
```
Content-Type: application/json
```

## 📋 All Available Endpoints

### Chat & AI
- `POST /v1/chat/completions` - OpenAI-compatible chat completions (Together AI)
- `GET /custom/:model` - Custom model chat (Flowith API) - **UPGRADED**
- `POST /v1/chat/completions/custom` - OpenAI-compatible custom models - **NEW**
- `GET /translate` - Text translation

### Image Generation
- `GET /create-v1` - Flux model image generation
- `GET /create-v2` - Aspect ratio controlled generation
- `GET /create-v3` - Pollinations AI generation
- `GET /create-v4` - Style preset generation
- `GET /create` - Perchance AI generation
- `GET /img2img` - Image-to-image transformation

### Utilities
- `GET /check` - Username availability checker
- `GET /Geo-detect` - IP geolocation and device detection
- `GET /tts` - Text-to-speech conversion
- `GET /temp-email/new` - Generate temporary email
- `GET /temp-email/inbox` - Check email inbox
- `GET /temp-email/view` - View email content

### Health & System
- `GET /ping` - Simple health check
- `GET /health` - Detailed system status
- `GET /v1/models` - Available AI models

## 🔗 Quick Links

- [Chat API Examples](./examples.md#chat-examples)
- [Image Generation Examples](./examples.md#image-examples)
- [Error Handling](./examples.md#error-handling)
- [Rate Limits](./examples.md#rate-limits)

## 📞 Support

For issues or questions, please refer to the main README.md or create an issue in the repository.
