# Chat & AI Endpoints

## POST /v1/chat/completions

OpenAI-compatible chat completions endpoint with streaming support.

### Request

```http
POST /v1/chat/completions
Content-Type: application/json
```

### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `model` | string | Yes | - | Model name (see available models) |
| `messages` | array | Yes | - | Array of message objects |
| `max_tokens` | integer | No | 512 | Maximum tokens to generate |
| `temperature` | float | No | 0.7 | Sampling temperature (0.0-2.0) |
| `stream` | boolean | No | false | Enable streaming responses |

### Message Object

```json
{
  "role": "system|user|assistant",
  "content": "Message content"
}
```

### Available Models

- `Llama-3.3-70B-Instruct-Turbo`
- `DeepSeek-R1-Distill-Llama-70B`
- `EXAONE-3.5-32B-Instruct`
- `EXAONE-Deep-32B`
- `AFM-4.5B-Preview`

### Examples

#### Simple Chat
```bash
curl -X POST "http://localhost:3000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "Llama-3.3-70B-Instruct-Turbo",
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user",
        "content": "Hello! How are you?"
      }
    ],
    "max_tokens": 150,
    "temperature": 0.7
  }'
```

#### Conversation with History
```bash
curl -X POST "http://localhost:3000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "Llama-3.3-70B-Instruct-Turbo",
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user",
        "content": "What is the capital of France?"
      },
      {
        "role": "assistant",
        "content": "The capital of France is Paris."
      },
      {
        "role": "user",
        "content": "What is the population of that city?"
      }
    ],
    "max_tokens": 200,
    "temperature": 0.5
  }'
```

#### Streaming Response
```bash
curl -X POST "http://localhost:3000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "Llama-3.3-70B-Instruct-Turbo",
    "messages": [
      {
        "role": "system",
        "content": "You are a creative storyteller."
      },
      {
        "role": "user",
        "content": "Tell me a short story about a robot."
      }
    ],
    "max_tokens": 300,
    "temperature": 0.8,
    "stream": true
  }'
```

### Response Format

#### Non-streaming Response
```json
{
  "id": "chatcmpl-uuid",
  "object": "chat.completion",
  "created": 1234567890,
  "model": "Llama-3.3-70B-Instruct-Turbo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! I'm doing well, thank you for asking..."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 43,
    "completion_tokens": 10,
    "total_tokens": 53
  }
}
```

#### Streaming Response
```
data: {"type":"metadata","model":"Llama-3.3-70B-Instruct-Turbo","timestamp":"2025-07-12T04:36:26.885Z"}

data: {"type":"content","content":"Hello","timestamp":"2025-07-12T04:36:28.139Z"}

data: {"type":"content","content":"!","timestamp":"2025-07-12T04:36:28.240Z"}

data: {"type":"done","full_content":"Hello! I'm doing well...","timestamp":"2025-07-12T04:36:29.049Z"}
```

## GET /custom/:model

Chat with custom models through Flowith API provider.

### Supported Models
- `grok-3-mini`
- `gemini-2.5-flash`
- `gpt-4.1-nano`
- `gpt-4.1-mini`

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `model` | string | Yes | Model name (in URL path) |
| `prompt` | string | Yes | User message |
| `system` | string | No | System message |
| `stream` | boolean | No | Enable streaming |

### Example
```bash
curl "http://localhost:3000/custom/grok-3-mini?prompt=Hello&system=You%20are%20helpful&stream=true"
```

## POST /v1/chat/completions/custom

OpenAI-compatible endpoint for custom models with Flowith API.

### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `model` | string | Yes | - | Model name (see supported models above) |
| `messages` | array | Yes | - | Array of message objects |
| `stream` | boolean | No | false | Enable streaming responses |
| `max_tokens` | integer | No | 512 | Maximum tokens to generate |
| `temperature` | float | No | 0.7 | Sampling temperature |

### Example
```bash
curl -X POST "http://localhost:3000/v1/chat/completions/custom" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "grok-3-mini",
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user",
        "content": "Hello! How are you?"
      }
    ],
    "stream": false
  }'
```

## GET /translate

Translate text using AI models.

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `text` | string | Yes | Text to translate |
| `lang` | string | Yes | Target language |

### Example
```bash
curl "http://localhost:3000/translate?text=Hello%20World&lang=Spanish"
```

### Response
```json
{
  "original": "Hello World",
  "translation": "Hola Mundo",
  "language": "Spanish",
  "model": "Llama-3.3-70B-Instruct-Turbo",
  "timestamp": "2025-07-12T04:36:29.049Z"
}
```
