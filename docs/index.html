<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FluxAPI Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            padding: 20px;
            background: #f8f9fa;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .card h3 {
            margin-top: 0;
            color: #2980b9;
        }
        .card p {
            color: #666;
            margin-bottom: 15px;
        }
        .card a {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
        }
        .card a:hover {
            text-decoration: underline;
        }
        .quick-start {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .quick-start h3 {
            margin-top: 0;
            color: white;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .endpoint-list {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .endpoint-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .endpoint-list li {
            margin: 5px 0;
        }
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            background: #27ae60;
            color: white;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e1e8ed;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 FluxAPI Documentation</h1>
        <p>Complete API documentation for FluxAPI - A comprehensive Node.js API providing AI chat, image generation, and utility services.</p>
        
        <div class="status-badge">✅ All Systems Operational</div>

        <div class="quick-start">
            <h3>🏃‍♂️ Quick Start</h3>
            <p>Get started with FluxAPI in seconds:</p>
            <div class="code-block">
curl -X POST "http://localhost:3000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{"model": "Llama-3.3-70B-Instruct-Turbo", "messages": [{"role": "user", "content": "Hello!"}]}'
            </div>
        </div>

        <h2>📚 Documentation Sections</h2>
        
        <div class="grid">
            <div class="card">
                <h3>💬 Chat & AI</h3>
                <p>OpenAI-compatible chat completions with streaming support, custom models, and translation services.</p>
                <div class="endpoint-list">
                    <strong>Key Endpoints:</strong>
                    <ul>
                        <li>POST /v1/chat/completions</li>
                        <li>GET /custom/:model</li>
                        <li>GET /translate</li>
                    </ul>
                </div>
                <a href="chat-ai.md">View Chat Documentation →</a>
            </div>

            <div class="card">
                <h3>🎨 Image Generation</h3>
                <p>Multiple AI image generation endpoints with different models, styles, and transformation capabilities.</p>
                <div class="endpoint-list">
                    <strong>Key Endpoints:</strong>
                    <ul>
                        <li>GET /create-v1 to /create-v4</li>
                        <li>GET /img2img</li>
                        <li>GET /create-v1.1-text-to-video</li>
                    </ul>
                </div>
                <a href="image-generation.md">View Image Documentation →</a>
            </div>

            <div class="card">
                <h3>🛠️ Utilities</h3>
                <p>Username checking across 40+ platforms, geolocation, text-to-speech, and temporary email services.</p>
                <div class="endpoint-list">
                    <strong>Key Endpoints:</strong>
                    <ul>
                        <li>GET /check</li>
                        <li>GET /Geo-detect</li>
                        <li>GET /tts</li>
                        <li>GET /temp-email/*</li>
                    </ul>
                </div>
                <a href="utilities.md">View Utilities Documentation →</a>
            </div>

            <div class="card">
                <h3>❤️ Health & System</h3>
                <p>Health checks, system status monitoring, and available AI models information.</p>
                <div class="endpoint-list">
                    <strong>Key Endpoints:</strong>
                    <ul>
                        <li>GET /ping</li>
                        <li>GET /health</li>
                        <li>GET /v1/models</li>
                    </ul>
                </div>
                <a href="health-system.md">View Health Documentation →</a>
            </div>

            <div class="card">
                <h3>💻 Examples & Integration</h3>
                <p>Code examples, integration guides, error handling, and best practices for all endpoints.</p>
                <div class="endpoint-list">
                    <strong>Includes:</strong>
                    <ul>
                        <li>JavaScript/Node.js examples</li>
                        <li>Python integration</li>
                        <li>Streaming implementations</li>
                        <li>Error handling patterns</li>
                    </ul>
                </div>
                <a href="examples.md">View Examples →</a>
            </div>

            <div class="card">
                <h3>📖 Complete API Reference</h3>
                <p>Comprehensive reference with all endpoints, parameters, response formats, and troubleshooting guide.</p>
                <div class="endpoint-list">
                    <strong>Features:</strong>
                    <ul>
                        <li>Quick reference table</li>
                        <li>HTTP status codes</li>
                        <li>Error codes & solutions</li>
                        <li>Rate limits & best practices</li>
                    </ul>
                </div>
                <a href="api-reference.md">View API Reference →</a>
            </div>
        </div>

        <h2>🔗 Quick Links</h2>
        <div class="endpoint-list">
            <ul>
                <li><strong>Base URL:</strong> http://localhost:3000</li>
                <li><strong>Health Check:</strong> <a href="http://localhost:3000/ping">http://localhost:3000/ping</a></li>
                <li><strong>Available Models:</strong> <a href="http://localhost:3000/v1/models">http://localhost:3000/v1/models</a></li>
                <li><strong>System Status:</strong> <a href="http://localhost:3000/health">http://localhost:3000/health</a></li>
            </ul>
        </div>

        <h2>🚀 Features</h2>
        <div class="grid">
            <div style="text-align: center; padding: 20px;">
                <h4>🤖 OpenAI Compatible</h4>
                <p>Fully compatible with OpenAI API format</p>
            </div>
            <div style="text-align: center; padding: 20px;">
                <h4>🌊 Real-time Streaming</h4>
                <p>Server-Sent Events for live responses</p>
            </div>
            <div style="text-align: center; padding: 20px;">
                <h4>🎨 Multiple AI Models</h4>
                <p>5+ AI models for different use cases</p>
            </div>
            <div style="text-align: center; padding: 20px;">
                <h4>🛠️ Rich Utilities</h4>
                <p>Username checking, geolocation, TTS, and more</p>
            </div>
        </div>

        <div class="footer">
            <p>FluxAPI Documentation • Built with ❤️ for developers</p>
            <p>For support and issues, please refer to the main repository</p>
        </div>
    </div>
</body>
</html>
