# FluxAPI Complete Reference

## Base Information

- **Base URL**: `http://localhost:3000`
- **Content-Type**: `application/json` (for POST requests)
- **Authentication**: RapidAPI proxy secret header (for most endpoints)

## Quick Reference Table

| Endpoint | Method | Purpose | Key Parameters |
|----------|--------|---------|----------------|
| `/ping` | GET | Health check | None |
| `/health` | GET | System status | None |
| `/v1/models` | GET | List AI models | None |
| `/v1/chat/completions` | POST | OpenAI-compatible chat | `model`, `messages`, `stream` |
| `/custom/:model` | GET | Custom model chat | `model`, `prompt`, `stream` |
| `/translate` | GET | Text translation | `text`, `lang` |
| `/create-v1` | GET | Flux image generation | `prompt`, `size`, `style` |
| `/create-v2` | GET | Aspect ratio images | `prompt`, `aspect_ratio` |
| `/create-v3` | GET | Pollinations images | `prompt`, `width`, `height` |
| `/create-v4` | GET | Style preset images | `prompt`, `size`, `style` |
| `/create` | GET | Perchance images | `prompt`, `size`, `style` |
| `/img2img` | GET | Image transformation | `image`, `prompt`, `aspect_ratio` |
| `/create-v1.1-text-to-video` | GET | Text to video | `prompt`, `steps` |
| `/check` | GET | Username checker | `username` |
| `/Geo-detect` | GET | IP geolocation | None |
| `/tts` | GET | Text to speech | `content`, `voice_id` |
| `/temp-email/new` | GET | Generate temp email | None |
| `/temp-email/inbox` | GET | Check email inbox | `secretKey` |
| `/temp-email/view` | GET | View email content | `id` |

## Response Formats

### Success Response (Chat)
```json
{
  "id": "chatcmpl-uuid",
  "object": "chat.completion",
  "created": 1234567890,
  "model": "model-name",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Response text"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 20,
    "total_tokens": 30
  }
}
```

### Streaming Response (Chat)
```
data: {"type":"metadata","model":"model-name","timestamp":"2025-07-12T10:30:00Z"}

data: {"type":"content","content":"Hello","timestamp":"2025-07-12T10:30:01Z"}

data: {"type":"done","full_content":"Hello World","timestamp":"2025-07-12T10:30:02Z"}
```

### Error Response
```json
{
  "error": {
    "message": "Error description",
    "type": "error_type",
    "code": "error_code"
  }
}
```

## HTTP Status Codes

| Code | Meaning | When Used |
|------|---------|-----------|
| 200 | OK | Successful request |
| 400 | Bad Request | Missing/invalid parameters |
| 403 | Forbidden | Invalid API key/authentication |
| 404 | Not Found | Resource not found |
| 500 | Internal Server Error | Server-side error |
| 503 | Service Unavailable | Service temporarily down |

## Model Information

### Available Chat Models

| Alias | Provider ID | Description |
|-------|-------------|-------------|
| `Llama-3.3-70B-Instruct-Turbo` | `meta-llama/Llama-3.3-70B-Instruct-Turbo-Free` | Meta's 70B parameter model |
| `DeepSeek-R1-Distill-Llama-70B` | `deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free` | DeepSeek distilled model |
| `EXAONE-3.5-32B-Instruct` | `lgai/exaone-3-5-32b-instruct` | EXAONE instruction model |
| `EXAONE-Deep-32B` | `lgai/exaone-deep-32b` | EXAONE deep model |
| `AFM-4.5B-Preview` | `arcee-ai/AFM-4.5B-Preview` | AFM preview model |

## Image Generation Styles

### /create-v4 Styles
- `default` - No style modification
- `cyberpunk` - Futuristic cyberpunk aesthetic
- `anime` - Vibrant anime illustration
- `pixelart` - 8-bit retro pixel art
- `oilpaint` - Classic oil painting
- `3d` - Detailed 3D render

### Common Image Sizes
- `512x512` - Square, small
- `1024x1024` - Square, standard
- `1024x768` - Landscape
- `768x1024` - Portrait
- `1920x1080` - Widescreen

## Supported Platforms (Username Checker)

### Social Media
- Twitter, Instagram, Facebook, TikTok, Snapchat, LinkedIn, Threads

### Professional
- GitHub, LinkedIn, Medium, Dev.to, CodePen, Replit

### Communication
- Discord, Telegram, WhatsApp, Mastodon, Bluesky

### Content Creation
- YouTube, Twitch, SoundCloud, Bandcamp, Substack

### Business
- Gumroad, Ko-fi, BuyMeACoffee, ProductHunt

### Gaming & Tech
- Steam, Epic Games, Itch.io, HackerNews

## Rate Limits & Best Practices

### Recommended Limits
- Chat API: 10 requests/minute
- Image Generation: 5 requests/minute
- Username Checker: 20 requests/minute
- Utility Services: 30 requests/minute

### Best Practices
1. **Implement exponential backoff** for failed requests
2. **Cache responses** when appropriate (especially models list)
3. **Use streaming** for long-form content generation
4. **Handle errors gracefully** with user-friendly messages
5. **Validate inputs** before sending requests

## Security Headers

### Required Headers
```http
X-RapidAPI-Proxy-Secret: your-secret-key
Content-Type: application/json
```

### Optional Headers
```http
User-Agent: YourApp/1.0
Accept: application/json
```

## Environment Variables

```env
PORT=3000
DEV_MODE=false
TOGETHER_API_KEY=your-together-ai-key
RAPIDAPI_PROXY_SECRET=your-rapidapi-secret
```

## Common Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| `missing_required_parameter` | Required parameter missing | Check request parameters |
| `invalid_request_error` | Invalid request format | Validate JSON structure |
| `model_not_found` | Model doesn't exist | Use `/v1/models` to get valid models |
| `together_ai_error` | AI service issue | Check API key and service status |
| `rate_limit_exceeded` | Too many requests | Implement rate limiting |
| `service_unavailable` | External service down | Retry with exponential backoff |

## Integration Libraries

### Recommended Libraries

#### JavaScript/Node.js
- `node-fetch` or `axios` for HTTP requests
- `eventsource` for streaming support
- `form-data` for file uploads

#### Python
- `requests` for HTTP requests
- `sseclient` for streaming support
- `aiohttp` for async requests

#### cURL Examples
```bash
# Basic chat
curl -X POST "http://localhost:3000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{"model":"Llama-3.3-70B-Instruct-Turbo","messages":[{"role":"user","content":"Hello"}]}'

# Image generation
curl "http://localhost:3000/create-v4?prompt=sunset&size=1024x1024&style=anime"

# Username check
curl "http://localhost:3000/check?username=testuser"
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure server is running on port 3000
   - Check firewall settings

2. **Authentication Errors**
   - Verify RapidAPI proxy secret
   - Check Together AI API key

3. **Model Errors**
   - Use exact model names from `/v1/models`
   - Verify Together AI service status

4. **Image Generation Fails**
   - Check prompt length (keep under 500 characters)
   - Verify image size format (WIDTHxHEIGHT)

5. **Streaming Issues**
   - Ensure proper SSE handling
   - Check network connectivity

### Debug Mode
Set `DEV_MODE=true` to skip authentication for testing.
