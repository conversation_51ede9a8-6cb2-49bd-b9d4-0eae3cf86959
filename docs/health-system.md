# Health & System Endpoints

## GET /ping

Simple health check endpoint for basic connectivity testing.

### Parameters
None required.

### Example
```bash
curl "http://localhost:3000/ping"
```

### Response
```json
{
  "message": "pong"
}
```

### Use Cases
- Load balancer health checks
- Basic connectivity testing
- Uptime monitoring
- Quick API availability verification

## GET /health

Detailed system health and status information.

### Parameters
None required.

### Example
```bash
curl "http://localhost:3000/health"
```

### Response
```json
{
  "status": "healthy",
  "timestamp": "2025-07-12T10:30:00.000Z",
  "storage": "In-Memory",
  "models_available": 5
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `status` | string | Overall system status ("healthy", "degraded", "unhealthy") |
| `timestamp` | string | Current server timestamp in ISO format |
| `storage` | string | Storage system type |
| `models_available` | number | Number of available AI models |

### Use Cases
- Detailed system monitoring
- Service status dashboards
- Debugging and diagnostics
- Performance monitoring

## GET /v1/models

List all available AI models and their information.

### Parameters
None required.

### Example
```bash
curl "http://localhost:3000/v1/models"
```

### Response
```json
{
  "models": [
    {
      "alias": "Llama-3.3-70B-Instruct-Turbo",
      "id": "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
      "description": "Meta's 70B parameter Llama 3 model (turbo version)"
    },
    {
      "alias": "DeepSeek-R1-Distill-Llama-70B",
      "id": "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free",
      "description": "DeepSeek's distilled version of Llama 70B"
    },
    {
      "alias": "EXAONE-3.5-32B-Instruct",
      "id": "lgai/exaone-3-5-32b-instruct",
      "description": "EXAONE"
    },
    {
      "alias": "EXAONE-Deep-32B",
      "id": "lgai/exaone-deep-32b",
      "description": "EXAONE"
    },
    {
      "alias": "AFM-4.5B-Preview",
      "id": "arcee-ai/AFM-4.5B-Preview",
      "description": "AFM"
    }
  ]
}
```

### Model Object Fields

| Field | Type | Description |
|-------|------|-------------|
| `alias` | string | Short, user-friendly model name |
| `id` | string | Full model identifier used by the AI provider |
| `description` | string | Human-readable model description |

### Use Cases
- Model discovery for chat applications
- Dynamic model selection in UIs
- API capability documentation
- Integration planning

## System Status Codes

### HTTP Status Codes

| Code | Status | Description |
|------|--------|-------------|
| 200 | OK | Service is healthy and responding normally |
| 503 | Service Unavailable | Service is temporarily unavailable |
| 500 | Internal Server Error | Unexpected server error occurred |

### Health Status Values

| Status | Description |
|--------|-------------|
| `healthy` | All systems operational |
| `degraded` | Some non-critical issues present |
| `unhealthy` | Critical issues affecting functionality |

## Monitoring Best Practices

### Health Check Intervals
- **Ping endpoint**: Every 30 seconds for load balancers
- **Health endpoint**: Every 2-5 minutes for detailed monitoring
- **Models endpoint**: Once per hour or on application startup

### Error Handling
```bash
# Check if service is responding
if curl -f -s "http://localhost:3000/ping" > /dev/null; then
    echo "Service is up"
else
    echo "Service is down"
fi
```

### Automated Monitoring Script
```bash
#!/bin/bash
HEALTH_URL="http://localhost:3000/health"
RESPONSE=$(curl -s "$HEALTH_URL")
STATUS=$(echo "$RESPONSE" | jq -r '.status')

if [ "$STATUS" = "healthy" ]; then
    echo "✅ Service is healthy"
    exit 0
else
    echo "❌ Service is $STATUS"
    echo "$RESPONSE"
    exit 1
fi
```

## Integration Examples

### Docker Health Check
```dockerfile
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/ping || exit 1
```

### Kubernetes Liveness Probe
```yaml
livenessProbe:
  httpGet:
    path: /ping
    port: 3000
  initialDelaySeconds: 30
  periodSeconds: 10
```

### Kubernetes Readiness Probe
```yaml
readinessProbe:
  httpGet:
    path: /health
    port: 3000
  initialDelaySeconds: 5
  periodSeconds: 5
```

## Troubleshooting

### Common Issues

1. **Service Not Responding**
   - Check if the server is running
   - Verify port 3000 is accessible
   - Check firewall settings

2. **Degraded Status**
   - Check external API dependencies
   - Verify AI model availability
   - Monitor resource usage

3. **Model List Empty**
   - Verify Together AI configuration
   - Check API key validity
   - Review model configuration in code

## Response Time Benchmarks

| Endpoint | Expected Response Time |
|----------|----------------------|
| `/ping` | < 50ms |
| `/health` | < 100ms |
| `/v1/models` | < 200ms |
