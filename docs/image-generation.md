# Image Generation Endpoints

## GET /create-v1

Generate images using Flux model with style options.

### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `prompt` | string | Yes | - | Image description |
| `size` | string | No | "1024x1024" | Image dimensions |
| `style` | string | No | "Default" | Style preset |

### Available Styles
- Default, Cyberpunk, Anime, Pixelart, Oilpaint, 3D

### Example
```bash
curl "http://localhost:3000/create-v1?prompt=a%20beautiful%20sunset&size=1024x1024&style=Anime"
```

### Response
```json
{
  "code": 0,
  "message": "Success",
  "data": {
    "api": "https://rapidapi.com/FreeCode911/api/flux-api-4-custom-models-100-style",
    "prompt": "a beautiful sunset",
    "size": "1024x1024",
    "style": "Anime",
    "model": "flux-1-dev",
    "images": [
      {
        "url": "https://cdn.picgenv.net/fluxai/image.png"
      }
    ]
  },
  "timestamp": 1234567890
}
```

## GET /create-v2

Generate images with aspect ratio control.

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `prompt` | string | Yes | Image description |
| `aspect_ratio` | string | Yes | Aspect ratio (1:1, 2:3, 3:2) |

### Example
```bash
curl "http://localhost:3000/create-v2?prompt=mountain%20landscape&aspect_ratio=3:2"
```

### Response
```json
{
  "status": "success",
  "model": "flux shell fast",
  "aspect_ratio": "3:2",
  "api": "https://rapidapi.com/FreeCode911/api/flux-api-4-custom-models-100-style",
  "image_link": "https://cdn.picgenv.net/fluxai2/image.png"
}
```

## GET /create-v3

Generate images using Pollinations AI.

### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `prompt` | string | Yes | - | Image description |
| `width` | string | No | "384" | Image width |
| `height` | string | No | "384" | Image height |
| `seed` | string | No | random | Random seed |

### Example
```bash
curl "http://localhost:3000/create-v3?prompt=abstract%20art&width=512&height=512"
```

### Response
Returns the image directly as PNG data.

## GET /create-v4

Generate images with advanced style presets.

### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `prompt` | string | Yes | - | Image description |
| `size` | string | No | "1024x1024" | Image dimensions (WIDTHxHEIGHT) |
| `style` | string | No | "default" | Style preset |

### Available Styles
- `default` - No style modification
- `cyberpunk` - Futuristic cyberpunk cityscape
- `anime` - Vibrant anime illustration style
- `pixelart` - 8-bit retro pixel art
- `oilpaint` - Classic oil painting style
- `3d` - Highly detailed 3D render

### Example
```bash
curl "http://localhost:3000/create-v4?prompt=a%20robot%20in%20a%20garden&size=1024x1024&style=cyberpunk"
```

### Response
Returns the image directly as PNG data.

## GET /create

Generate images using Perchance AI.

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `prompt` | string | Yes | Image description |
| `negativePrompt` | string | No | What to avoid in image |
| `size` | string | Yes | Image dimensions (widthxheight) |
| `style` | string | No | Style preset |

### Example
```bash
curl "http://localhost:3000/create?prompt=fantasy%20castle&size=512x512&style=RANDOM"
```

### Response
```json
{
  "status": "DONE",
  "style": "Fantasy",
  "safeState": "SAFE",
  "negativePrompt": "",
  "url": "https://cdn.picgenv.net/perchance/image.png"
}
```

## GET /img2img

Transform existing images using AI.

### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `image` | string | Yes | - | Source image URL |
| `prompt` | string | Yes | - | Transformation description |
| `aspect_ratio` | string | No | "1:1" | Output aspect ratio |

### Example
```bash
curl "http://localhost:3000/img2img?image=https://example.com/image.jpg&prompt=make%20it%20artistic&aspect_ratio=1:1"
```

### Response
```json
{
  "status": "completed",
  "model": "kling-v1",
  "prompt": "make it artistic",
  "aspect_ratio": "1:1",
  "images": [
    {
      "url": "https://cdn.picgenv.net/fluxai/transformed.png"
    }
  ],
  "timestamp": 1234567890
}
```

## GET /create-v1.1-text-to-video

Generate videos from text descriptions.

### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `prompt` | string | Yes | - | Video description |
| `user_id` | string | No | "1" | User identifier |
| `steps` | string | No | "50" | Generation steps |

### Example
```bash
curl "http://localhost:3000/create-v1.1-text-to-video?prompt=a%20cat%20playing%20in%20garden&steps=50"
```

### Response
```json
{
  "status": "success",
  "video_url": "https://cdn.picgenv.net/video.mp4"
}
```

## Error Responses

All image generation endpoints may return these error formats:

```json
{
  "error": "Missing required parameter: prompt"
}
```

```json
{
  "error": "Invalid size format. Use WIDTHxHEIGHT like 1024x1024"
}
```

```json
{
  "error": "Internal server error"
}
```
