# Examples & Integration Guide

## Chat Examples

### Basic Chat Integration

#### JavaScript/Node.js
```javascript
const fetch = require('node-fetch');

async function chatWithAI(message, conversationHistory = []) {
  const messages = [
    { role: "system", content: "You are a helpful assistant." },
    ...conversationHistory,
    { role: "user", content: message }
  ];

  const response = await fetch('http://localhost:3000/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: "Llama-3.3-70B-Instruct-Turbo",
      messages: messages,
      max_tokens: 150,
      temperature: 0.7
    })
  });

  const data = await response.json();
  return data.choices[0].message.content;
}

// Usage
chatWithAI("Hello!").then(response => {
  console.log("AI:", response);
});
```

#### Python
```python
import requests
import json

def chat_with_ai(message, conversation_history=None):
    if conversation_history is None:
        conversation_history = []
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        *conversation_history,
        {"role": "user", "content": message}
    ]
    
    response = requests.post(
        'http://localhost:3000/v1/chat/completions',
        headers={'Content-Type': 'application/json'},
        json={
            "model": "Llama-3.3-70B-Instruct-Turbo",
            "messages": messages,
            "max_tokens": 150,
            "temperature": 0.7
        }
    )
    
    data = response.json()
    return data['choices'][0]['message']['content']

# Usage
response = chat_with_ai("Hello!")
print(f"AI: {response}")
```

### Streaming Chat

#### JavaScript with Server-Sent Events
```javascript
async function streamChat(message) {
  const response = await fetch('http://localhost:3000/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: "Llama-3.3-70B-Instruct-Turbo",
      messages: [
        { role: "system", content: "You are a helpful assistant." },
        { role: "user", content: message }
      ],
      max_tokens: 300,
      temperature: 0.8,
      stream: true
    })
  });

  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  let fullContent = '';

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    const chunk = decoder.decode(value);
    const lines = chunk.split('\n');

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const data = line.slice(6);
        if (data.trim() && data !== '[DONE]') {
          try {
            const parsed = JSON.parse(data);
            if (parsed.type === 'content') {
              process.stdout.write(parsed.content);
              fullContent += parsed.content;
            } else if (parsed.type === 'done') {
              console.log('\n--- Stream completed ---');
            }
          } catch (e) {
            // Skip invalid JSON
          }
        }
      }
    }
  }

  return fullContent;
}

// Usage
streamChat("Tell me a story about a robot").then(content => {
  console.log("\nFull story:", content);
});
```

### Conversation Management

#### Maintaining Context
```javascript
class ChatSession {
  constructor() {
    this.messages = [
      { role: "system", content: "You are a helpful assistant." }
    ];
  }

  async sendMessage(userMessage) {
    // Add user message to history
    this.messages.push({ role: "user", content: userMessage });

    const response = await fetch('http://localhost:3000/v1/chat/completions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: "Llama-3.3-70B-Instruct-Turbo",
        messages: this.messages,
        max_tokens: 150,
        temperature: 0.7
      })
    });

    const data = await response.json();
    const assistantMessage = data.choices[0].message.content;

    // Add assistant response to history
    this.messages.push({ role: "assistant", content: assistantMessage });

    return assistantMessage;
  }

  clearHistory() {
    this.messages = [
      { role: "system", content: "You are a helpful assistant." }
    ];
  }
}

// Usage
const chat = new ChatSession();
chat.sendMessage("What's 2+2?").then(response => {
  console.log("AI:", response);
  return chat.sendMessage("What about 3+3?");
}).then(response => {
  console.log("AI:", response);
});
```

## Image Examples

### Generate Image with Different Styles

#### Basic Image Generation
```bash
# Simple image
curl "http://localhost:3000/create-v4?prompt=a%20beautiful%20sunset&size=1024x1024"

# Anime style
curl "http://localhost:3000/create-v4?prompt=a%20robot%20in%20garden&size=1024x1024&style=anime"

# Cyberpunk style
curl "http://localhost:3000/create-v4?prompt=futuristic%20city&size=1024x1024&style=cyberpunk"
```

#### JavaScript Image Generation
```javascript
async function generateImage(prompt, style = 'default') {
  const response = await fetch(
    `http://localhost:3000/create-v4?prompt=${encodeURIComponent(prompt)}&size=1024x1024&style=${style}`
  );
  
  if (response.ok) {
    const imageBlob = await response.blob();
    const imageUrl = URL.createObjectURL(imageBlob);
    return imageUrl;
  } else {
    throw new Error('Image generation failed');
  }
}

// Usage
generateImage("a magical forest", "anime").then(imageUrl => {
  console.log("Generated image URL:", imageUrl);
  // Use imageUrl to display the image
});
```

### Image-to-Image Transformation
```javascript
async function transformImage(sourceImageUrl, prompt) {
  const response = await fetch(
    `http://localhost:3000/img2img?image=${encodeURIComponent(sourceImageUrl)}&prompt=${encodeURIComponent(prompt)}&aspect_ratio=1:1`
  );
  
  const data = await response.json();
  return data.images[0].url;
}

// Usage
transformImage("https://example.com/photo.jpg", "make it artistic")
  .then(transformedUrl => {
    console.log("Transformed image:", transformedUrl);
  });
```

## Utility Examples

### Username Availability Checker
```javascript
async function checkUsername(username) {
  const response = await fetch(
    `http://localhost:3000/check?username=${encodeURIComponent(username)}`
  );
  
  const data = await response.json();
  
  // Find available platforms
  const available = Object.entries(data.platforms)
    .filter(([platform, info]) => !info.exists)
    .map(([platform]) => platform);
  
  // Find taken platforms
  const taken = Object.entries(data.platforms)
    .filter(([platform, info]) => info.exists)
    .map(([platform, info]) => ({ platform, url: info.url }));
  
  return { available, taken };
}

// Usage
checkUsername("johndoe").then(result => {
  console.log("Available on:", result.available);
  console.log("Taken on:", result.taken);
});
```

### Text-to-Speech
```javascript
async function textToSpeech(text, voiceId = 'af_bella') {
  const response = await fetch(
    `http://localhost:3000/tts?content=${encodeURIComponent(text)}&voice_id=${voiceId}`
  );
  
  const data = await response.json();
  return data.audio_url;
}

// Usage
textToSpeech("Hello, this is a test").then(audioUrl => {
  console.log("Audio URL:", audioUrl);
  // Play the audio
  const audio = new Audio(audioUrl);
  audio.play();
});
```

## Error Handling

### Comprehensive Error Handling
```javascript
async function safeApiCall(url, options = {}) {
  try {
    const response = await fetch(url, options);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API Error (${response.status}): ${errorData.error?.message || errorData.error || 'Unknown error'}`);
    }
    
    return await response.json();
  } catch (error) {
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('Network error: Unable to connect to API');
    }
    throw error;
  }
}

// Usage with chat
async function safeChatRequest(message) {
  try {
    const data = await safeApiCall('http://localhost:3000/v1/chat/completions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: "Llama-3.3-70B-Instruct-Turbo",
        messages: [{ role: "user", content: message }],
        max_tokens: 150
      })
    });
    
    return data.choices[0].message.content;
  } catch (error) {
    console.error('Chat request failed:', error.message);
    return "Sorry, I'm having trouble responding right now.";
  }
}
```

## Rate Limits

### Implementing Rate Limiting
```javascript
class RateLimiter {
  constructor(maxRequests = 10, windowMs = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    this.requests = [];
  }

  canMakeRequest() {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    return this.requests.length < this.maxRequests;
  }

  recordRequest() {
    this.requests.push(Date.now());
  }

  async waitForSlot() {
    while (!this.canMakeRequest()) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    this.recordRequest();
  }
}

// Usage
const rateLimiter = new RateLimiter(5, 60000); // 5 requests per minute

async function rateLimitedChat(message) {
  await rateLimiter.waitForSlot();
  return await chatWithAI(message);
}
```

## Testing

### Unit Test Example (Jest)
```javascript
const fetch = require('node-fetch');

describe('FluxAPI Tests', () => {
  test('ping endpoint should return pong', async () => {
    const response = await fetch('http://localhost:3000/ping');
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data.message).toBe('pong');
  });

  test('chat completion should return valid response', async () => {
    const response = await fetch('http://localhost:3000/v1/chat/completions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: "Llama-3.3-70B-Instruct-Turbo",
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 50
      })
    });
    
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data).toHaveProperty('choices');
    expect(data.choices[0]).toHaveProperty('message');
    expect(data.choices[0].message).toHaveProperty('content');
  });
});
```
