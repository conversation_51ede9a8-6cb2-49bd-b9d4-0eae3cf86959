# Utility Services

## GET /check

Check username availability across 40+ social media platforms.

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `username` | string | Yes | Username to check |

### Supported Platforms

- **Social Media**: Twitter, Instagram, Facebook, TikTok, Snapchat, LinkedIn
- **Professional**: GitHub, LinkedIn, Medium, Dev.to, CodePen
- **Communication**: Discord, Telegram, WhatsApp, Threads
- **Content**: YouTube, Twitch, SoundCloud, Bandcamp
- **Business**: Gumroad, Ko-fi, BuyMeACoffee, ProductHunt
- **And many more...**

### Example
```bash
curl "http://localhost:3000/check?username=johndoe"
```

### Response
```json
{
  "username": "johndoe",
  "platforms": {
    "twitter": {
      "exists": true,
      "url": "https://twitter.com/johndoe"
    },
    "github": {
      "exists": false,
      "url": null
    },
    "instagram": {
      "exists": true,
      "url": "https://www.instagram.com/johndoe"
    }
  },
  "timestamp": 1234567890
}
```

## GET /Geo-detect

Get IP geolocation and device information.

### Parameters
No parameters required. Uses client IP automatically.

### Example
```bash
curl "http://localhost:3000/Geo-detect"
```

### Response
```json
{
  "ip": "***********",
  "geo": {
    "city": "New York",
    "region": "New York",
    "country": "US",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "timezone": "America/New_York"
  },
  "device": {
    "browser": "Chrome",
    "browserVersion": "91.0.4472.124",
    "os": "Windows",
    "osVersion": "10",
    "device": "unknown",
    "type": "desktop",
    "engine": "Blink"
  },
  "headers": {
    "accept-language": "en-US,en;q=0.9",
    "referer": "https://example.com",
    "user-agent": "Mozilla/5.0..."
  },
  "timestamp": 1234567890
}
```

## GET /tts

Convert text to speech audio.

### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `content` | string | Yes | - | Text to convert |
| `voice_id` | string | No | "af_bella" | Voice identifier |
| `category` | string | No | "en" | Language category |

### Example
```bash
curl "http://localhost:3000/tts?content=Hello%20World&voice_id=af_bella&category=en"
```

### Response
```json
{
  "status": "success",
  "voice_id": "af_bella",
  "category": "en",
  "content": "Hello World",
  "audio_url": "https://cdn.picgenv.net/tts/audio.mp3",
  "timestamp": 1234567890
}
```

## Temporary Email Services

### GET /temp-email/new

Generate a new temporary email address.

### Example
```bash
curl "http://localhost:3000/temp-email/new"
```

### Response
```json
{
  "address": "<EMAIL>",
  "secretKey": "secret-key-for-access"
}
```

### GET /temp-email/inbox

Check inbox for a temporary email.

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `secretKey` | string | Yes | Secret key from email generation |

### Example
```bash
curl "http://localhost:3000/temp-email/inbox?secretKey=your-secret-key"
```

### Response
```json
{
  "emails": [
    {
      "id": "email-id-123",
      "from": "<EMAIL>",
      "subject": "Welcome!",
      "date": "2025-07-12T10:30:00Z",
      "preview": "Welcome to our service..."
    }
  ]
}
```

### GET /temp-email/view

View specific email content.

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | Email ID from inbox |

### Example
```bash
curl "http://localhost:3000/temp-email/view?id=email-id-123"
```

### Response
Returns the email content as HTML.

## Error Responses

### Missing Parameters
```json
{
  "error": "Missing required parameter: username"
}
```

### Invalid Parameters
```json
{
  "error": "Missing secretKey parameter"
}
```

### Service Errors
```json
{
  "error": "TTS generation timed out or failed"
}
```

```json
{
  "error": "Failed to fetch email view"
}
```

### Rate Limiting
Some services may return rate limiting errors:
```json
{
  "error": "Rate limit exceeded. Please try again later."
}
```

## Usage Notes

1. **Username Checker**: Results depend on platform availability and may have rate limits
2. **Geo Detection**: Uses external IP geolocation services which may have quotas
3. **TTS Service**: Audio generation may take several seconds
4. **Temporary Email**: Emails expire after a certain time period
5. **All Services**: Some endpoints depend on external APIs and may occasionally be unavailable
